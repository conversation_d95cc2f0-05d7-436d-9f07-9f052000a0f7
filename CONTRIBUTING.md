# Contributing

Hey there, I am really excited that you want to contribute something to go-ios :-)

[go-ios](https://github.com/danielpaulus/go-ios) is [MIT]-licensed and accepts contributions via GitHub pull requests.
This document outlines some of the conventions on development workflow, commit message formatting, contact points, and other resources to make it easier to get your contribution accepted.

## Getting Started

- Fork the repository on GitHub
- Install golang and run go build
- Plug in any iOS device and run commands
- Make your changes

## Contribution Flow

This is a outline of what a contributor's workflow looks like:

- Create a separate branch from `main` branch to base your work
- Make commits of logical units
- Make sure your commit messages are in the proper format (see below)
- Push your changes to a topic branch in your fork of the repository
- Make sure to proofread the content before submitting
- Submit a pull request to the original repository

#### How to report a bug

Easy, create an issue ideally using one of the templates!

#### How to discuss enhancements or features you want to add

Easy, start a Github discussion!

## Code of Conduct

### My Pledge

In the interest of fostering an open and welcoming environment, we as contributors and maintainers pledge to make participation
in our project and our community a harassment-free experience for everyone,
regardless of age, body size, disability, ethnicity, gender identity and expression, level of experience, nationality, personal appearance, race, religion, or sexual identity and orientation.

### Our Responsibilities

Project maintainers have the right and responsibility to remove, edit, or reject comments, commits, code, wiki edits, issues, and other contributions that are not aligned to this Code of Conduct, or to ban temporarily or permanently any contributor for other behaviors that they deem inappropriate, threatening, offensive, or harmful.

[MIT]: ./LICENSE
