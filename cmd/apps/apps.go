package apps

import (
	"encoding/json"
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/installationproxy"
)

type Options struct {
	*options.Options

	System       bool
	All          bool
	List         bool
	FileSharing  bool
	JSONDisabled bool
	PrettyJSON   bool
}

// NewAppsCmd creates a new apps command
func NewAppsCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "apps",
		Short: "List installed applications",
		Long: `Retrieves a list of installed applications. 
--system prints out preinstalled system apps. 
--all prints all apps, including system, user, and hidden apps. 
--list only prints bundle ID, bundle name and version number. 
--file-sharing only prints apps which enable documents sharing.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return printInstalledApps(dev, o)
		},
	}

	cmd.Flags().BoolVar(&o.System, "system", false, "Print out preinstalled system apps")
	cmd.Flags().BoolVar(&o.All, "all", false, "Print all apps, including system, user, and hidden apps")
	cmd.Flags().BoolVar(&o.List, "list", false, "Only print bundle ID, bundle name and version number")
	cmd.Flags().BoolVar(&o.FileSharing, "file-sharing", false, "Only print apps which enable documents sharing")

	return cmd
}

func printInstalledApps(device ios.DeviceEntry, opts *Options) error {
	svc, err := installationproxy.New(device)
	if err != nil {
		return fmt.Errorf("error creating installation proxy: %w", err)
	}

	var apps []installationproxy.AppInfo
	if opts.All {
		apps, err = svc.BrowseAllApps()
	} else if opts.System {
		apps, err = svc.BrowseSystemApps()
	} else if opts.FileSharing {
		apps, err = svc.BrowseFileSharingApps()
	} else {
		apps, err = svc.BrowseUserApps()
	}

	if err != nil {
		return fmt.Errorf("error browsing apps: %w", err)
	}

	if opts.JSONDisabled {
		if opts.List {
			for _, app := range apps {
				fmt.Printf("%s, %s, %s\n", app.CFBundleIdentifier, app.CFBundleDisplayName, app.CFBundleVersion)
			}
		} else {
			for _, app := range apps {
				fmt.Printf("Bundle ID: %s\n", app.CFBundleIdentifier)
				fmt.Printf("  Name: %s\n", app.CFBundleDisplayName)
				fmt.Printf("  Version: %s\n", app.CFBundleVersion)
				fmt.Printf("  Type: %s\n", app.ApplicationType)
				fmt.Printf("  Path: %s\n", app.Path)
				fmt.Println()
			}
		}
	} else {
		var jsonData []byte
		if opts.PrettyJSON {
			jsonData, err = json.MarshalIndent(apps, "", "  ")
		} else {
			jsonData, err = json.Marshal(apps)
		}

		if err != nil {
			return fmt.Errorf("error marshaling JSON: %w", err)
		}

		fmt.Println(string(jsonData))
	}

	return nil
}
