package httpproxy

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/mcinstall"
)

type Options struct {
	*options.Options
	P12File     string
	P12Password string
}

// NewHttpProxyCmd creates a new http-proxy command
func NewHttpProxyCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "http-proxy",
		Short: "Manage HTTP proxy settings",
		Long:  `Set or remove global HTTP proxy on supervised device.`,
	}

	// Add flags
	cmd.PersistentFlags().StringVar(&o.P12File, "p12file", "", "P12 certificate file for supervised devices")
	cmd.PersistentFlags().StringVar(&o.P12Password, "password", "", "P12 certificate password (or use P12_PASSWORD env var)")

	// Add subcommands
	cmd.AddCommand(newSetCmd(o))
	cmd.AddCommand(newRemoveCmd(o))

	return cmd
}

func newSetCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "set <host> <port> [user] [pass]",
		Short: "Set HTTP proxy",
		Long: `Set global HTTP proxy on supervised device. Use the password argument or set the environment variable 'P12_PASSWORD'.
Specify proxy password either as argument or using the environment var: PROXY_PASSWORD.
Use p12 file and password for silent installation on supervised devices.`,
		Args: cobra.RangeArgs(2, 4),
		RunE: func(cmd *cobra.Command, args []string) error {
			host := args[0]
			port := args[1]
			var user, pass string
			if len(args) > 2 {
				user = args[2]
			}
			if len(args) > 3 {
				pass = args[3]
			}
			if pass == "" {
				pass = os.Getenv("PROXY_PASSWORD")
			}
			return runHttpProxySet(opts, host, port, user, pass)
		},
	}
}

func newRemoveCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "remove",
		Short: "Remove HTTP proxy",
		Long:  `Removes the global HTTP proxy config. Only works with HTTP proxies set by go-ios!`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runHttpProxyRemove(opts)
		},
	}
}

func runHttpProxySet(opts *Options, host, port, user, pass string) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	if opts.P12Password == "" {
		opts.P12Password = os.Getenv("P12_PASSWORD")
	}

	var p12bytes []byte
	if opts.P12File != "" {
		p12bytes, err = os.ReadFile(opts.P12File)
		if err != nil {
			return fmt.Errorf("error reading P12 file: %w", err)
		}
	}

	err = mcinstall.SetHttpProxy(device, host, port, user, pass, p12bytes, opts.P12Password)
	if err != nil {
		return fmt.Errorf("error setting HTTP proxy: %w", err)
	}

	fmt.Printf("HTTP proxy set to %s:%s\n", host, port)
	return nil
}

func runHttpProxyRemove(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	err = mcinstall.RemoveProxy(device)
	if err != nil {
		return fmt.Errorf("error removing HTTP proxy: %w", err)
	}

	fmt.Println("HTTP proxy removed")
	return nil
}
