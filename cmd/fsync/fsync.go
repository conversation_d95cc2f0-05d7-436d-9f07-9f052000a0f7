package fsync

import (
	"fmt"
	"os"
	"path"
	"path/filepath"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/afc"
)

type Options struct {
	*options.Options
	App       string
	SrcPath   string
	DstPath   string
	Path      string
	Recursive bool
}

// NewFSyncCmd creates a new fsync command
func NewFSyncCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "fsync",
		Short: "File synchronization operations",
		Long:  `Pull or Push files from/to device, or perform file operations like rm, tree, mkdir.`,
	}

	// Add subcommands
	cmd.AddCommand(newPullCmd(o))
	cmd.AddCommand(newPushCmd(o))
	cmd.AddCommand(newRmCmd(o))
	cmd.AddCommand(newTreeCmd(o))
	cmd.AddCommand(newMkdirCmd(o))

	cmd.PersistentFlags().StringVar(&o.App, "app", "", "Bundle ID for app container")

	return cmd
}

func newPullCmd(opts *Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "pull",
		Short: "Pull file from device",
		Long:  `Pull file from srcPath to dstPath.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runFSyncPull(opts)
		},
	}

	cmd.Flags().StringVar(&opts.SrcPath, "srcPath", "", "Source path on device")
	cmd.Flags().StringVar(&opts.DstPath, "dstPath", "", "Destination path on host")
	_ = cmd.MarkFlagRequired("srcPath")
	_ = cmd.MarkFlagRequired("dstPath")

	return cmd
}

func newPushCmd(opts *Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "push",
		Short: "Push file to device",
		Long:  `Push file from srcPath to dstPath.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runFSyncPush(opts)
		},
	}

	cmd.Flags().StringVar(&opts.SrcPath, "srcPath", "", "Source path on host")
	cmd.Flags().StringVar(&opts.DstPath, "dstPath", "", "Destination path on device")
	_ = cmd.MarkFlagRequired("srcPath")
	_ = cmd.MarkFlagRequired("dstPath")

	return cmd
}

func newRmCmd(opts *Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "rm",
		Short: "Remove files/directories",
		Long:  `Remove files or directories from device.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runFSyncRm(opts)
		},
	}

	cmd.Flags().StringVar(&opts.Path, "path", "", "Target path on device")
	cmd.Flags().BoolVarP(&opts.Recursive, "recursive", "r", false, "Recursively remove all files and directories")
	_ = cmd.MarkFlagRequired("path")

	return cmd
}

func newTreeCmd(opts *Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "tree",
		Short: "Show directory tree",
		Long:  `Show directory tree view.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runFSyncTree(opts)
		},
	}

	cmd.Flags().StringVar(&opts.Path, "path", "", "Target path on device")
	_ = cmd.MarkFlagRequired("path")

	return cmd
}

func newMkdirCmd(opts *Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "mkdir",
		Short: "Create directory",
		Long:  `Create directory on device.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runFSyncMkdir(opts)
		},
	}

	cmd.Flags().StringVar(&opts.Path, "path", "", "Target path on device")
	_ = cmd.MarkFlagRequired("path")

	return cmd
}

func getAFCService(device ios.DeviceEntry, containerBundleId string) (*afc.Connection, error) {
	if containerBundleId == "" {
		return afc.New(device)
	}
	return afc.NewContainer(device, containerBundleId)
}

func runFSyncPull(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	afcService, err := getAFCService(device, opts.App)
	if err != nil {
		return fmt.Errorf("error creating AFC service: %w", err)
	}
	defer afcService.Close()

	if opts.DstPath != "" {
		if exists, _ := ios.PathExists(opts.DstPath); !exists {
			err = os.MkdirAll(opts.DstPath, os.ModePerm)
			if err != nil {
				return fmt.Errorf("error creating directory: %w", err)
			}
		}
	}

	dstPath := path.Join(opts.DstPath, filepath.Base(opts.SrcPath))
	err = afcService.Pull(opts.SrcPath, dstPath)
	if err != nil {
		return fmt.Errorf("error pulling file: %w", err)
	}

	fmt.Printf("File pulled from %s to %s\n", opts.SrcPath, dstPath)
	return nil
}

func runFSyncPush(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	afcService, err := getAFCService(device, opts.App)
	if err != nil {
		return fmt.Errorf("error creating AFC service: %w", err)
	}
	defer afcService.Close()

	err = afcService.Push(opts.SrcPath, opts.DstPath)
	if err != nil {
		return fmt.Errorf("error pushing file: %w", err)
	}

	fmt.Printf("File pushed from %s to %s\n", opts.SrcPath, opts.DstPath)
	return nil
}

func runFSyncRm(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	afcService, err := getAFCService(device, opts.App)
	if err != nil {
		return fmt.Errorf("error creating AFC service: %w", err)
	}
	defer afcService.Close()

	if opts.Recursive {
		err = afcService.RemoveAll(opts.Path)
	} else {
		err = afcService.Remove(opts.Path)
	}

	if err != nil {
		return fmt.Errorf("error removing file/directory: %w", err)
	}

	fmt.Printf("Removed %s\n", opts.Path)
	return nil
}

func runFSyncTree(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	afcService, err := getAFCService(device, opts.App)
	if err != nil {
		return fmt.Errorf("error creating AFC service: %w", err)
	}
	defer afcService.Close()

	err = afcService.TreeView(opts.Path, "", true)
	if err != nil {
		return fmt.Errorf("error showing tree view: %w", err)
	}

	return nil
}

func runFSyncMkdir(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	afcService, err := getAFCService(device, opts.App)
	if err != nil {
		return fmt.Errorf("error creating AFC service: %w", err)
	}
	defer afcService.Close()

	err = afcService.MkDir(opts.Path)
	if err != nil {
		return fmt.Errorf("error creating directory: %w", err)
	}

	fmt.Printf("Directory created: %s\n", opts.Path)
	return nil
}
