package dproxy

import (
	"fmt"
	"log/slog"
	"os"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/debugproxy"
)

type Options struct {
	*options.Options

	Binary bool
}

// NewDProxyCmd creates a new dproxy command
func NewDProxyCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "dproxy",
		Short: "Start reverse engineering proxy server",
		Long: `Starts the reverse engineering proxy server.
It dumps every communication in plain text so it can be implemented easily.
Use "sudo launchctl unload -w /Library/Apple/System/Library/LaunchDaemons/com.apple.usbmuxd.plist"
to stop usbmuxd and load to start it again should the proxy mess up things.
The --binary flag will dump everything in raw binary without any decoding.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runDProxy(o)
		},
	}

	cmd.Flags().BoolVar(&o.Binary, "binary", false, "Dump everything in raw binary without any decoding")

	return cmd
}

func runDProxy(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	// Configure slog for text output for debug proxy
	handler := slog.NewTextHandler(os.Stderr, &slog.HandlerOptions{Level: slog.LevelDebug})
	slog.SetDefault(slog.New(handler))

	proxy := debugproxy.NewDebugProxy()
	err = proxy.Launch(device, opts.Binary)
	if err != nil {
		return fmt.Errorf("error starting debug proxy: %w", err)
	}

	return nil
}
