package devmode

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/amfi"
	"github.com/danielpaulus/go-ios/ios/imagemounter"
)

type Options struct {
	*options.Options

	EnablePostRestart bool
}

// NewDevModeCmd creates a new dev-mode command
func NewDevModeCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "dev-mode",
		Short: "Manage developer mode",
		Long:  `Enable developer mode on the device or check if it is enabled. Can also completely finalize developer mode setup after device is restarted.`,
	}

	// Add flags
	cmd.PersistentFlags().BoolVar(&o.EnablePostRestart, "enable-post-restart", false, "Enable post-restart setup")

	// Add subcommands
	cmd.AddCommand(newEnableCmd(o))
	cmd.AddCommand(newGetCmd(o))

	return cmd
}

func newEnableCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "enable",
		Short: "Enable developer mode",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runDevModeEnable(opts)
		},
	}
}

func newGetCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "get",
		Short: "Get developer mode status",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runDevModeGet(opts)
		},
	}
}

func runDevModeEnable(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	err = amfi.EnableDeveloperMode(device, opts.EnablePostRestart)
	if err != nil {
		return fmt.Errorf("error enabling developer mode: %w", err)
	}

	fmt.Println("Developer mode enabled")
	return nil
}

func runDevModeGet(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	enabled, err := imagemounter.IsDevModeEnabled(device)
	if err != nil {
		return fmt.Errorf("error checking developer mode status: %w", err)
	}

	result := map[string]any{
		"developer_mode_enabled": enabled,
	}

	return helpers.PrintJSON(result, opts.Options)
}
