package assistivetouch

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

type Options struct {
	*options.Options

	Force bool
}

// NewAssistiveTouchCmd creates a new assistive-touch command
func NewAssistiveTouchCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "assistive-touch",
		Short: "Control AssistiveTouch feature",
		Long:  `Enables, disables, toggles, or returns the state of the "AssistiveTouch" software home-screen button. iOS 11+ only (Use --force to try on older versions).`,
	}

	// Add flags
	cmd.PersistentFlags().BoolVar(&o.Force, "force", false, "Force operation on older iOS versions")

	// Add subcommands
	cmd.AddCommand(newEnableCmd(o))
	cmd.AddCommand(newDisableCmd(o))
	cmd.AddCommand(newToggleCmd(o))
	cmd.AddCommand(newGetCmd(o))

	return cmd
}

func newEnableCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "enable",
		Short: "Enable AssistiveTouch",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runAssistiveTouch(opts, "enable")
		},
	}
}

func newDisableCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "disable",
		Short: "Disable AssistiveTouch",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runAssistiveTouch(opts, "disable")
		},
	}
}

func newToggleCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "toggle",
		Short: "Toggle AssistiveTouch",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runAssistiveTouch(opts, "toggle")
		},
	}
}

func newGetCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "get",
		Short: "Get AssistiveTouch state",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runAssistiveTouch(opts, "get")
		},
	}
}

func runAssistiveTouch(opts *Options, action string) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	switch action {
	case "enable":
		err = ios.SetAssistiveTouch(device, true)
		if err != nil {
			return fmt.Errorf("error enabling AssistiveTouch: %w", err)
		}
		fmt.Println("AssistiveTouch enabled")
	case "disable":
		err = ios.SetAssistiveTouch(device, false)
		if err != nil {
			return fmt.Errorf("error disabling AssistiveTouch: %w", err)
		}
		fmt.Println("AssistiveTouch disabled")
	case "toggle":
		current, err := ios.GetAssistiveTouch(device)
		if err != nil {
			return fmt.Errorf("error getting current AssistiveTouch state: %w", err)
		}

		err = ios.SetAssistiveTouch(device, !current)
		if err != nil {
			return fmt.Errorf("error toggling AssistiveTouch: %w", err)
		}

		if current {
			fmt.Println("AssistiveTouch disabled")
		} else {
			fmt.Println("AssistiveTouch enabled")
		}
	case "get":
		state, err := ios.GetAssistiveTouch(device)
		if err != nil {
			return fmt.Errorf("error getting AssistiveTouch state: %w", err)
		}

		return helpers.PrintJSON(map[string]any{
			"assistivetouch_enabled": state,
		}, opts.Options)
	default:
		return fmt.Errorf("unknown action: %s", action)
	}

	return nil
}
