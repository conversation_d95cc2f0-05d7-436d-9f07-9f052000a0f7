package batteryregistry

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/diagnostics"
)

// NewBatteryRegistryCmd creates a new battery-registry command
func NewBatteryRegistryCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "battery-registry",
		Short: "Print battery registry statistics",
		Long:  `Prints battery registry stats like Temperature, Voltage.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runBatteryRegistry(opts)
		},
	}
}

func runBatteryRegistry(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	diagnosticsService, err := diagnostics.New(device)
	if err != nil {
		return fmt.Errorf("error creating diagnostics service: %w", err)
	}
	defer func(diagnosticsService *diagnostics.Connection) {
		if diagnosticsService != nil {
			_ = diagnosticsService.Close()
		}
	}(diagnosticsService)

	batteryRegistry, err := diagnosticsService.Battery()
	if err != nil {
		return fmt.Errorf("error getting battery registry: %w", err)
	}

	return helpers.PrintJSON(batteryRegistry, opts)
}
