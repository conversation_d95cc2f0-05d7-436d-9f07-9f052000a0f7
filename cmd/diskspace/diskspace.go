package diskspace

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/afc"
)

// NewDiskSpaceCmd creates a new diskspace command
func NewDiskSpaceCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "diskspace",
		Short: "Print disk space information",
		Long:  `Prints disk space info.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runDiskSpace(opts)
		},
	}
}

func runDiskSpace(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	afcService, err := afc.New(device)
	if err != nil {
		return fmt.Errorf("error creating AFC service: %w", err)
	}
	defer afcService.Close()

	info, err := afcService.GetSpaceInfo()
	if err != nil {
		return fmt.Errorf("error getting disk space info: %w", err)
	}

	if opts.JSONDisabled {
		fmt.Printf("      Model: %s\n", info.Model)
		fmt.Printf("  BlockSize: %d\n", info.BlockSize/8)
		fmt.Printf("  FreeSpace: %s\n", ios.ByteCountDecimal(int64(info.FreeBytes)))
		fmt.Printf("  UsedSpace: %s\n", ios.ByteCountDecimal(int64(info.TotalBytes-info.FreeBytes)))
		fmt.Printf(" TotalSpace: %s\n", ios.ByteCountDecimal(int64(info.TotalBytes)))
		return nil
	}

	result := map[string]any{
		"model":       info.Model,
		"block_size":  info.BlockSize / 8,
		"free_bytes":  info.FreeBytes,
		"used_bytes":  info.TotalBytes - info.FreeBytes,
		"total_bytes": info.TotalBytes,
		"free_space":  ios.ByteCountDecimal(int64(info.FreeBytes)),
		"used_space":  ios.ByteCountDecimal(int64(info.TotalBytes - info.FreeBytes)),
		"total_space": ios.ByteCountDecimal(int64(info.TotalBytes)),
	}

	return helpers.PrintJSON(result, opts)
}
