package helpers

import (
	"encoding/json"
	"fmt"
	"log/slog"

	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/tunnel"
)

// GetDevice returns a device with the specified UDID
func GetDevice(opts *options.Options) (ios.DeviceEntry, error) {
	device, err := ios.GetDevice(opts.UDID)
	if err != nil {
		return device, err
	}

	// Check if we need to set up tunnel info
	userspaceTunnelHost := opts.UserspaceHost
	if userspaceTunnelHost == "" {
		userspaceTunnelHost = ios.HttpApiHost()
	}

	if opts.Address != "" && opts.RsdPort != 0 {
		if opts.UserspacePort != 0 {
			device.UserspaceTUN = true
			device.UserspaceTUNHost = userspaceTunnelHost
			device.UserspaceTUNPort = opts.UserspacePort
		}
		device = deviceWithRsdProvider(device, opts.UDID, opts.Address, opts.RsdPort)
	} else {
		tunnelInfoHost := opts.TunnelInfoHost
		if tunnelInfoHost == "" {
			tunnelInfoHost = ios.HttpApiHost()
		}
		tunnelInfoPort := opts.TunnelInfoPort
		if tunnelInfoPort == 0 {
			tunnelInfoPort = ios.HttpApiPort()
		}

		info, err := tunnel.TunnelInfoForDevice(device.Properties.SerialNumber, tunnelInfoHost, tunnelInfoPort)
		if err == nil {
			device.UserspaceTUNPort = info.UserspaceTUNPort
			device.UserspaceTUNHost = userspaceTunnelHost
			device.UserspaceTUN = info.UserspaceTUN
			device = deviceWithRsdProvider(device, opts.UDID, info.Address, info.RsdPort)
		} else {
			slog.Warn("failed to get tunnel info", "udid", device.Properties.SerialNumber)
		}
	}

	return device, nil
}

// deviceWithRsdProvider is a helper function to set up RSD provider for a device
func deviceWithRsdProvider(device ios.DeviceEntry, udid, address string, rsdPort int) ios.DeviceEntry {
	slog.Debug("using device with rsd provider",
		"udid", udid,
		"address", address,
		"rsdPort", rsdPort)

	rsdService, err := ios.NewWithAddrPortDevice(address, rsdPort, device)
	if err != nil {
		slog.Error("could not connect to RSD", "error", err)
		return device
	}
	defer func(rsdService *ios.RsdService) {
		if rsdService != nil {
			_ = rsdService.Close()
		}
	}(rsdService)

	rsdProvider, err := rsdService.Handshake()
	if err != nil {
		slog.Error("failed to handshake with RSD", "error", err)
		return device
	}

	device1, err := ios.GetDeviceWithAddress(udid, address, rsdProvider)
	if err != nil {
		slog.Error("error getting devicelist", "error", err)
		return device
	}

	device1.UserspaceTUN = device.UserspaceTUN
	device1.UserspaceTUNHost = device.UserspaceTUNHost
	device1.UserspaceTUNPort = device.UserspaceTUNPort

	return device1
}

// ConvertToJSONString converts an object to a JSON string
func ConvertToJSONString(obj any, prettyJSON bool) (string, error) {
	var (
		jsonData []byte
		err      error
	)

	if prettyJSON {
		jsonData, err = json.MarshalIndent(obj, "", "  ")
	} else {
		jsonData, err = json.Marshal(obj)
	}
	if err != nil {
		return "", fmt.Errorf("error marshaling JSON: %w", err)
	}

	return string(jsonData), nil
}

// PrintJSON prints an object as JSON to stdout
func PrintJSON(obj any, opts *options.Options) error {
	if opts.JSONDisabled {
		// For non-JSON output, try to print in a human-readable format
		fmt.Printf("%+v\n", obj)
		return nil
	}

	jsonStr, err := ConvertToJSONString(obj, opts.PrettyJSON)
	if err != nil {
		return err
	}

	fmt.Println(jsonStr)
	return nil
}
