package syslog

import (
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/syslog"
)

type Options struct {
	*options.Options

	Parse bool
}

// NewSyslogCmd creates a new syslog command
func NewSyslogCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "syslog",
		Short: "Print device logs",
		Long:  `Prints a device's log output. Use --parse to parse the fields from the log.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return runSyslog(dev, o)
		},
	}

	// Add flags
	cmd.Flags().BoolVar(&o.Parse, "parse", false, "Parse the fields from the log")

	return cmd
}

func runSyslog(device ios.DeviceEntry, opts *Options) error {
	syslogConnection, err := syslog.New(device)
	if err != nil {
		return fmt.Errorf("error creating syslog connection: %w", err)
	}

	errCh := make(chan error, 1)
	signalCh := make(chan os.Signal, 1)
	signal.Notify(signalCh, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-signalCh
		slog.Info("Stopping syslog")

		if syslogConnection != nil {
			_ = syslogConnection.Close()
		}

		errCh <- nil
	}()

	slog.Info("Streaming syslog. Press Ctrl+C to stop.")

	// Create a function to format log messages
	var logFormatter func(string) (string, error)
	if opts.JSONDisabled {
		logFormatter = func(msg string) (string, error) {
			return msg, nil
		}
	} else {
		if opts.Parse {
			logFormatter = func(msg string) (string, error) {
				parser := syslog.Parser()
				entry, err := parser(msg)
				if err != nil {
					return helpers.ConvertToJSONString(map[string]string{"message": msg}, opts.PrettyJSON)
				}

				return helpers.ConvertToJSONString(entry, opts.PrettyJSON)
			}
		} else {
			logFormatter = func(msg string) (string, error) {
				return helpers.ConvertToJSONString(map[string]string{"message": msg}, opts.PrettyJSON)
			}
		}
	}

	// Start reading log messages
	go func() {
		for {
			logMessage, err := syslogConnection.ReadLogMessage()
			if err != nil {
				errCh <- fmt.Errorf("error reading syslog message: %w", err)
				break
			}
			logMessage = strings.TrimSuffix(logMessage, "\x00")
			logMessage = strings.TrimSuffix(logMessage, "\x0A")

			s, err := logFormatter(logMessage)
			if err != nil {
				errCh <- fmt.Errorf("error formatting syslog message: %w", err)
				break
			}
			fmt.Println(s)
		}
	}()

	return <-errCh
}
