package pair

import (
	"fmt"
	"os"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

type Options struct {
	*options.Options
	P12File  string
	Password string
}

// NewPairCmd creates a new pair command
func NewPairCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	pairCmd := &cobra.Command{
		Use:   "pair",
		Short: "Pair with a device",
		Long:  `Pairs the device. If the device is supervised, specify the path to the p12 file to pair without a trust dialog. Specify the password either with the argument or by setting the environment variable 'P12_PASSWORD'.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			if o.Password == "" {
				o.Password = os.Getenv("P12_PASSWORD")
			}

			dev, err := helpers.GetDevice(o.Options)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return pairDevice(dev, o.P12File, o.Password)
		},
	}

	// Add flags
	pairCmd.Flags().StringVar(&o.P12File, "p12file", "", "Path to the p12 file for supervised devices")
	pairCmd.Flags().StringVar(&o.Password, "password", "", "Password for the p12 file")

	return pairCmd
}

func pairDevice(device ios.DeviceEntry, p12FilePath, p12Password string) error {
	if p12FilePath != "" {
		// Supervised pairing
		p12Bytes, err := os.ReadFile(p12FilePath)
		if err != nil {
			return fmt.Errorf("error reading p12 file: %w", err)
		}

		if p12Password == "" {
			return fmt.Errorf("P12 password is required. Set it with --password or P12_PASSWORD environment variable")
		}

		err = ios.PairSupervised(device, p12Bytes, p12Password)
		if err != nil {
			return fmt.Errorf("error pairing supervised device: %w", err)
		}

		log.Info("Device paired successfully")
		return nil
	}

	// Normal pairing
	err := ios.Pair(device)
	if err != nil {
		return fmt.Errorf(
			"error pairing device: %w\nIf this is the first time pairing, please accept the trust dialog on the device and run the command again",
			err,
		)
	}

	log.Info("Device paired successfully")
	return nil
}
