package version

import (
	"fmt"
	"runtime/debug"

	"github.com/spf13/cobra"
)

// Version is the version of the application
const Version = "local-build"

// NewVersionCmd creates a new version command
func NewVersionCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "version",
		Short: "Print the version number of go-ios",
		Long:  `Print the version number of go-ios`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return printVersion()
		},
	}
}

func printVersion() error {
	fmt.Printf("go-ios %s\n", Version)
	if info, ok := debug.ReadBuildInfo(); ok {
		fmt.Println("go version:", info.GoVersion)
		for _, setting := range info.Settings {
			if setting.Key == "vcs.revision" {
				fmt.Println("git commit:", setting.Value)
			}
			if setting.Key == "vcs.time" {
				fmt.Println("build time:", setting.Value)
			}
		}
	}
	return nil
}
