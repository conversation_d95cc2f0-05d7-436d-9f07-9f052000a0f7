package configure

import (
	"bytes"
	"fmt"
	"os"
	"os/exec"
	"strings"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"
)

// NewConfigureCmd creates a new configure command
func NewConfigureCmd() *cobra.Command {
	configureCmd := &cobra.Command{
		Use:   "configure",
		Short: "Configure the system for go-ios development",
		Long:  `Installs necessary dependencies for building go-ios on Debian, Ubuntu, or Alpine Linux.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runConfigure()
		},
	}

	return configureCmd
}

func runConfigure() error {
	if !IsDebianUbuntuOrAlpine() {
		return fmt.Errorf("only ubuntu, debian or alpine are supported")
	}
	if !DpkgExists() && !ApkExists() {
		return fmt.Errorf("dpkg or apk needs to be installed to check dependencies")
	}

	err := checkDep("libusb-1.0-0-dev")
	if err != nil {
		return err
	}

	err = checkDep("build-essential")
	if err != nil {
		return err
	}

	err = checkDep("pkg-config")
	if err != nil {
		return err
	}

	log.Info("good to go. run 'make'")
	return nil
}

func checkDep(dep string) error {
	log.Info("checking: " + dep)
	if !CheckPackageInstalled(dep) {
		log.Info("installing: " + dep)
		err := InstallPackage(dep)
		if err != nil {
			return fmt.Errorf("failed to install %s: %w", dep, err)
		}
	}
	log.Info("ok: " + dep)
	return nil
}

// CheckPackageInstalled checks if the specified package is installed.
// It uses dpkg on Debian/Ubuntu and apk on Alpine.
func CheckPackageInstalled(packageName string) bool {
	if DpkgExists() {
		output := ExecuteCommand("dpkg", "-l", packageName)
		return strings.Contains(output, packageName) && !strings.Contains(output, "no packages found")
	} else if ApkExists() {
		output := ExecuteCommand("apk", "info", packageName)
		return output != ""
	}
	log.Error("No compatible package manager found (dpkg or apk).")
	return false
}

// ExecuteCommand runs the specified shell command and returns its output or logs if there's an error.
func ExecuteCommand(command string, args ...string) string {
	output, err := ExecuteCommandWithError(command, args...)
	if err != nil {
		log.Errorf("Failed to execute command: %s\nError: %v", command, err)
	}
	return output
}

// ExecuteCommandWithError runs the specified shell command and returns its output and any error.
func ExecuteCommandWithError(command string, args ...string) (string, error) {
	cmd := exec.Command(command, args...)
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	err := cmd.Run()
	if err != nil {
		return "", fmt.Errorf("failed to execute command: %s\nError: %v, Stderr: %v", command, err, stderr.String())
	}
	return out.String(), nil
}

// DpkgExists checks if dpkg exists in the system's PATH.
func DpkgExists() bool {
	_, err := exec.LookPath("dpkg")
	return err == nil
}

// IsDebianUbuntuOrAlpine checks if the operating system is Debian, Ubuntu, or Alpine.
func IsDebianUbuntuOrAlpine() bool {
	content, err := os.ReadFile("/etc/os-release")
	if err != nil {
		log.Errorf("Failed to read /etc/os-release: %v", err)
		return false
	}

	osRelease := string(content)
	return strings.Contains(osRelease, "ID=debian") || strings.Contains(osRelease, "ID=ubuntu") || strings.Contains(osRelease, "ID=alpine")
}

func IsRunningWithSudo() bool {
	// The effective user ID (eUID) is 0 for the root user.
	return os.Geteuid() == 0
}

// PackageManagerType returns the type of package manager available on the system.
func PackageManagerType() string {
	if _, err := exec.LookPath("apt-get"); err == nil {
		return "apt-get"
	} else if _, err := exec.LookPath("apk"); err == nil {
		return "apk"
	}
	return ""
}

// InstallPackage installs a package using the system's package manager.
func InstallPackage(packageName string) error {
	var command string
	var args []string

	// Check if apt-get is available
	if _, err := exec.LookPath("apt-get"); err == nil {
		command = "apt-get"
		args = []string{"install", "-y", packageName}
	} else if _, err := exec.LookPath("apk"); err == nil {
		// If apt-get is not available, check for apk
		command = "apk"
		args = []string{"add", packageName}
	} else {
		return fmt.Errorf("no compatible package manager found (apt-get or apk)")
	}

	// Execute the install command
	log.Infof("Installing package %s using %s", packageName, command)
	_, err := ExecuteCommandWithError(command, args...)
	return err
}

// ApkExists checks if apk exists in the system's PATH.
func ApkExists() bool {
	_, err := exec.LookPath("apk")
	return err == nil
}
