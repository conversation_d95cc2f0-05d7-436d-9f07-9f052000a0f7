package setlocationgpx

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/simlocation"
)

type Options struct {
	*options.Options

	GPXFilePath string
}

// NewSetLocationGpxCmd creates a new set-location-gpx command
func NewSetLocationGpxCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "set-location-gpx",
		Short: "Set device location from GPX file",
		Long:  `Updates the location of the device based on the data in a GPX file. Example: set-location-gpx --gpx-file-path=/home/<USER>/location.gpx`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runSetLocationGpx(o)
		},
	}

	cmd.Flags().StringVar(&o.GP<PERSON>ath, "gpx-file-path", "", "Path to GPX file")
	_ = cmd.MarkFlagRequired("gpx-file-path")

	return cmd
}

func runSetLocationGpx(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	err = simlocation.SetLocationGPX(device, opts.GPXFilePath)
	if err != nil {
		return fmt.Errorf("error setting location from GPX file: %w", err)
	}

	fmt.Printf("Location set from GPX file: %s\n", opts.GPXFilePath)
	return nil
}
