package memlimitoff

import (
	"fmt"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

type Options struct {
	*options.Options
	Process string
}

// NewMemLimitOffCmd creates a new mem-limit-off command
func NewMemLimitOffCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "mem-limit-off",
		Short: "Disable memory limit for a process",
		Long:  `Waives memory limit set by iOS (For instance a Broadcast Extension limit is 50 MB).`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runMemLimitOff(o)
		},
	}

	cmd.Flags().StringVar(&o.Process, "process", "", "Process name to disable memory limit for")
	_ = cmd.MarkFlagRequired("process")

	return cmd
}

func runMemLimitOff(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	processControl, err := instruments.NewProcessControl(device)
	if err != nil {
		return fmt.Errorf("error creating process control: %w", err)
	}
	defer func(processControl *instruments.ProcessControl) {
		if processControl != nil {
			_ = processControl.Close()
		}
	}(processControl)

	deviceInfoService, err := instruments.NewDeviceInfoService(device)
	if err != nil {
		return fmt.Errorf("error creating device info service: %w", err)
	}
	defer deviceInfoService.Close()

	processList, err := deviceInfoService.ProcessList()
	if err != nil {
		return fmt.Errorf("error getting process list: %w", err)
	}

	found := false
	for _, process := range processList {
		if process.Pid > 1 && process.Name == opts.Process {
			disabled, err := processControl.DisableMemoryLimit(process.Pid)
			if err != nil {
				return fmt.Errorf("error disabling memory limit for process %s (PID: %d): %w", process.Name, process.Pid, err)
			}

			log.WithFields(log.Fields{
				"process": process.Name,
				"pid":     process.Pid,
			}).Infof("Memory limit disabled: %v", disabled)

			found = true
		}
	}

	if !found {
		return fmt.Errorf("process '%s' not found", opts.Process)
	}

	fmt.Printf("Memory limit disabled for process: %s\n", opts.Process)
	return nil
}
