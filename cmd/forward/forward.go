package forward

import (
	"fmt"
	"os"
	"os/signal"
	"syscall"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/forward"
)

type Options struct {
	*options.Options

	HostPort, TargetPort int
}

// NewForwardCmd creates a new forward command
func NewForwardCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "forward",
		Short: "Forward TCP connection",
		Long:  `Similar to iproxy, forward a TCP connection to the device.`,
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return startForwarding(dev, o)
		},
	}

	// Add flags
	cmd.Flags().IntVar(&o.HostPort, "host-port", 0, "Host port to forward")
	cmd.Flags().IntVar(&o.TargetPort, "target-port", 0, "Target port to forward")

	return cmd
}

func startForwarding(device ios.DeviceEntry, opts *Options) error {
	cl, err := forward.Forward(device, uint16(opts.HostPort), uint16(opts.TargetPort))
	if err != nil {
		return fmt.Errorf("error forwarding port: %w", err)
	}
	defer stopForwarding(cl)

	log.Infof("Forwarding local port %d to device port %d. Press Ctrl+C to stop.", opts.HostPort, opts.TargetPort)

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
	<-c

	log.Info("Stopping port forwarding")
	return nil
}

func stopForwarding(cl *forward.ConnListener) {
	err := cl.Close()
	if err != nil {
		log.Errorf("Error closing forwarded port: %v", err)
	}
}
