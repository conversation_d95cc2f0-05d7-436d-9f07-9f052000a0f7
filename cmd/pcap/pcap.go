package pcap

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/pcap"
)

type Options struct {
	*options.Options
	Pid     int
	Process string
}

// NewPcapCmd creates a new pcap command
func NewPcapCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "pcap",
		Short: "Start network packet capture",
		Long:  `Starts a pcap dump of network traffic, use --pid or --process to filter specific processes.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runPcap(o)
		},
	}

	cmd.Flags().IntVar(&o.Pid, "pid", 0, "Process ID to filter")
	cmd.Flags().StringVar(&o.Process, "process", "", "Process name to filter")

	return cmd
}

func runPcap(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	// Set global pcap options
	if opts.Pid > 0 {
		pcap.Pid = int32(opts.Pid)
	}
	if opts.Process != "" {
		pcap.ProcName = opts.Process
	}

	err = pcap.Start(device)
	if err != nil {
		return fmt.Errorf("error starting pcap: %w", err)
	}

	return nil
}
