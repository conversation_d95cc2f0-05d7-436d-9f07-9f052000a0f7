package rsd

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
)

// NewRsdCmd creates a new rsd command
func NewRsdCmd(opts *options.Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "rsd",
		Short: "Remote Service Discovery operations",
		Long:  `Remote Service Discovery operations.`,
	}

	// Add subcommands
	cmd.AddCommand(newLsCmd(opts))

	return cmd
}

func newLsCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "ls",
		Short: "List RSD services",
		Long:  `List RSD services and their port.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runRsdLs(opts)
		},
	}
}

func runRsdLs(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	if !device.SupportsRsd() {
		return fmt.Errorf("device does not support RSD")
	}

	services := device.Rsd.GetServices()

	if opts.JSONDisabled {
		fmt.Println(services)
		return nil
	}

	return helpers.PrintJSON(services, opts)
}
