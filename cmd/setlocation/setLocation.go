package setlocation

import (
	"fmt"
	"strconv"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/instruments"
	"github.com/danielpaulus/go-ios/ios/simlocation"
)

type Options struct {
	*options.Options
	Lat string
	Lon string
}

// NewSetLocationCmd creates a new set-location command
func NewSetLocationCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "set-location",
		Short: "Set device location",
		Long:  `Updates the location of the device to the provided by latitude and longitude coordinates. Example: set-location --lat=40.730610 --lon=-73.935242`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runSetLocation(o)
		},
	}

	cmd.Flags().StringVar(&o.Lat, "lat", "", "Latitude coordinate")
	cmd.Flags().StringVar(&o.Lon, "lon", "", "Longitude coordinate")
	_ = cmd.MarkFlagRequired("lat")
	_ = cmd.MarkFlagRequired("lon")

	return cmd
}

func runSetLocation(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	lat, err := strconv.ParseFloat(opts.Lat, 64)
	if err != nil {
		return fmt.Errorf("invalid latitude: %w", err)
	}

	lon, err := strconv.ParseFloat(opts.Lon, 64)
	if err != nil {
		return fmt.Errorf("invalid longitude: %w", err)
	}

	if device.SupportsRsd() {
		server, err := instruments.NewLocationSimulationService(device)
		if err != nil {
			return fmt.Errorf("error creating location simulation service: %w", err)
		}
		defer server.Close()

		err = server.StartSimulateLocation(lat, lon)
		if err != nil {
			return fmt.Errorf("error setting location via instruments: %w", err)
		}
	} else {
		err = simlocation.SetLocation(device, opts.Lat, opts.Lon)
		if err != nil {
			return fmt.Errorf("error setting location: %w", err)
		}
	}

	fmt.Printf("Location set to latitude: %f, longitude: %f\n", lat, lon)
	return nil
}
