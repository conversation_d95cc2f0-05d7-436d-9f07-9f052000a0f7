package mobilegestalt

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/diagnostics"
)

type Options struct {
	*options.Options
	Plist bool
}

// NewMobileGestaltCmd creates a new mobile-gestalt command
func NewMobileGestaltCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "mobile-gestalt <key>...",
		Short: "Query MobileGestalt keys",
		Long: `Lets you query mobile-gestalt keys. Standard output is json but if desired you can get
it in plist format by adding the --plist param.
Ex.: "ios mobile-gestalt MainScreenCanvasSizes ArtworkTraits --plist"`,
		Args: cobra.MinimumNArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runMobileGestalt(o, args)
		},
	}

	cmd.Flags().BoolVar(&o.Plist, "plist", false, "Output in plist format instead of JSON")

	return cmd
}

func runMobileGestalt(opts *Options, keys []string) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	conn, err := diagnostics.New(device)
	if err != nil {
		return fmt.Errorf("error creating diagnostics connection: %w", err)
	}
	defer func(conn *diagnostics.Connection) {
		if conn != nil {
			_ = conn.Close()
		}
	}(conn)

	result, err := conn.MobileGestaltQuery(keys)
	if err != nil {
		return fmt.Errorf("error getting MobileGestalt keys: %w", err)
	}

	if opts.Plist {
		plistData := ios.ToPlist(result)
		fmt.Print(plistData)
		return nil
	}

	return helpers.PrintJSON(result, opts.Options)
}
