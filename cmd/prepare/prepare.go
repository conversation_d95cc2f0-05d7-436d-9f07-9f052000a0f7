package prepare

import (
	"fmt"
	"os"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/mcinstall"
)

type Options struct {
	*options.Options

	SkipAll  bool
	Skip     []string
	CertFile string
	OrgName  string
	Locale   string
	Lang     string
}

// NewPrepareCmd creates a new prepare command
func NewPrepareCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "prepare",
		Short: "Prepare a device for development",
		Long: `Prepare a device. Use skip-all to skip everything multiple --skip args to skip only a subset.
You can use 'ios prepare printskip' to get a list of all options to skip. Use certfile and orgname if you want to supervise the device.
If you need certificates to supervise, run 'ios prepare create-cert' and go-ios will generate one you can use.
locale and lang are optional, the default is en_US and en.
Run 'ios lang' to see a list of all supported locales and languages.`,
	}

	// Add subcommands
	cmd.AddCommand(newRunCmd(o))
	cmd.AddCommand(newCreateCertCmd(o))
	cmd.AddCommand(newPrintSkipCmd(o))

	return cmd
}

func newRunCmd(opts *Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "run",
		Short: "Run device preparation",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runPrepare(opts)
		},
	}

	cmd.Flags().BoolVar(&opts.SkipAll, "skip-all", false, "Skip all setup options")
	cmd.Flags().StringSliceVar(&opts.Skip, "skip", []string{}, "Skip specific setup options")
	cmd.Flags().StringVar(&opts.CertFile, "certfile", "", "Certificate file path for supervision")
	cmd.Flags().StringVar(&opts.OrgName, "orgname", "", "Organization name for supervision")
	cmd.Flags().StringVar(&opts.Locale, "locale", "", "Device locale (default: en_US)")
	cmd.Flags().StringVar(&opts.Lang, "lang", "", "Device language (default: en)")

	return cmd
}

func newCreateCertCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "create-cert",
		Short: "Create supervision certificate",
		Long:  `A nice util to generate a certificate you can use for supervising devices. Make sure you rename and store it in a safe place.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runCreateCert(opts)
		},
	}
}

func newPrintSkipCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "printskip",
		Short: "Print all skip options",
		Long:  `Print all options you can skip.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runPrintSkip(opts)
		},
	}
}

func runPrepare(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	skip := mcinstall.GetAllSetupSkipOptions()
	if opts.SkipAll {
		skip = mcinstall.GetAllSetupSkipOptions()
	} else if len(opts.Skip) > 0 {
		skip = opts.Skip
	}

	var certBytes []byte
	if opts.CertFile != "" {
		certBytes, err = os.ReadFile(opts.CertFile)
		if err != nil {
			return fmt.Errorf("error reading certificate file: %w", err)
		}
		if opts.OrgName == "" {
			return fmt.Errorf("--orgname must be specified if certfile for supervision is provided")
		}
	}

	err = mcinstall.Prepare(device, skip, certBytes, opts.OrgName, opts.Locale, opts.Lang)
	if err != nil {
		return fmt.Errorf("error preparing device: %w", err)
	}

	result := map[string]any{
		"status":  "ok",
		"message": "Device prepared successfully",
	}

	return helpers.PrintJSON(result, opts.Options)
}

func runCreateCert(_ *Options) error {
	cert, err := ios.CreateDERFormattedSupervisionCert()
	if err != nil {
		return fmt.Errorf("error creating certificate: %w", err)
	}

	files := map[string][]byte{
		"supervision-cert.der":        cert.CertDER,
		"supervision-cert.pem":        cert.CertPEM,
		"supervision-private-key.key": cert.PrivateKeyDER,
		"supervision-private-key.pem": cert.PrivateKeyPEM,
		"supervision-csr.csr":         []byte(cert.Csr),
	}

	for filename, data := range files {
		err = os.WriteFile(filename, data, 0o600)
		if err != nil {
			return fmt.Errorf("error writing %s: %w", filename, err)
		}
		log.Infof("Created: %s", filename)
	}

	log.Info("Golang does not have good PKCS12 format sadly. If you need a p12 file run this: " +
		"'openssl pkcs12 -export -inkey supervision-private-key.pem -in supervision-cert.pem -out certificate.p12 -password pass:a'")

	return nil
}

func runPrintSkip(opts *Options) error {
	skipOptions := mcinstall.GetAllSetupSkipOptions()
	return helpers.PrintJSON(skipOptions, opts.Options)
}
