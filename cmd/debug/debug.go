package debug

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/debugserver"
)

type Options struct {
	*options.Options

	StopAtEntry bool
}

// NewDebugCmd creates a new debug command
func NewDebugCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "debug <app_path>",
		Short: "Start debug with lldb",
		Long:  `Start debug with lldb.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runDebug(o, args[0])
		},
	}

	cmd.Flags().BoolVar(&o.StopAtEntry, "stop-at-entry", false, "Stop at entry point")

	return cmd
}

func runDebug(opts *Options, appPath string) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	err = debugserver.Start(device, appPath, opts.StopAtEntry)
	if err != nil {
		return fmt.Errorf("error starting debug session: %w", err)
	}

	return nil
}
