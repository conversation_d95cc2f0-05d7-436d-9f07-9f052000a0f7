package instruments

import (
	"encoding/json"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

// NewInstrumentsCmd creates a new instruments command
func NewInstrumentsCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "instruments",
		Short: "Access instruments features",
		Long:  `Access instruments features for performance analysis.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runInstruments(opts)
		},
	}
}

func runInstruments(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	listenerFunc, closeFunc, err := instruments.ListenAppStateNotifications(device)
	if err != nil {
		return fmt.Errorf("error creating instruments listener: %w", err)
	}

	go func() {
		for {
			notification, err := listenerFunc()
			if err != nil {
				fmt.Printf("Error receiving notification: %v\n", err)
				return
			}
			s, _ := json.Marshal(notification)
			fmt.Println(string(s))
		}
	}()

	fmt.Println("Listening for application state notifications... Press Ctrl+C to stop")

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
	<-c

	fmt.Println("\nStopping instruments listener...")
	err = closeFunc()
	if err != nil {
		fmt.Printf("Warning: timeout during close %v\n", err)
	}

	return nil
}
