package ax

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/accessibility"
)

type Options struct {
	*options.Options

	FontSize int
}

// NewAxCmd creates a new ax command
func NewAxCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "ax",
		Short: "Access accessibility inspector features",
		Long:  `Access accessibility inspector features.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runAx(o)
		},
	}

	cmd.Flags().IntVar(&o.FontSize, "font", 0, "Font size for accessibility")

	return cmd
}

func runAx(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	accessibilityService, err := accessibility.NewWithoutEventChangeListeners(device)
	if err != nil {
		return fmt.Errorf("error creating accessibility service: %w", err)
	}

	// Initialize the service
	err = accessibilityService.SwitchToDevice()
	if err != nil {
		return fmt.Errorf("error switching to device: %w", err)
	}

	// If font size is specified, set it
	if opts.FontSize > 0 {
		err = accessibilityService.UpdateAccessibilitySetting("FontSize", opts.FontSize)
		if err != nil {
			return fmt.Errorf("error setting font size: %w", err)
		}
		fmt.Printf("Font size set to %d\n", opts.FontSize)
	}

	result := map[string]any{
		"status":  "accessibility service connected",
		"message": "Use accessibility inspector features",
	}

	return helpers.PrintJSON(result, opts.Options)
}
