package devicestate

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/diagnostics"
)

// NewDeviceStateCmd creates a new device-state command
func NewDeviceStateCmd(opts *options.Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "device-state",
		Short: "Manage device state profiles",
		Long:  `Manage device state profiles like slow network conditions, GPU settings, etc.`,
	}

	// Add subcommands
	cmd.AddCommand(newListCmd(opts))
	cmd.AddCommand(newEnableCmd(opts))

	return cmd
}

func newListCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "list",
		Short: "List supported device conditions",
		Long:  `Prints a list of all supported device conditions, like slow network, gpu etc.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runDeviceStateList(opts)
		},
	}
}

func newEnableCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "enable <profileTypeId> <profileId>",
		Short: "Enable a device state profile",
		Long:  `Enables a profile with ids (use the list command to see options). It will only stay active until the process is terminated. Ex. "ios devicestate enable SlowNetworkCondition SlowNetwork3GGood"`,
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runDeviceStateEnable(opts, args[0], args[1])
		},
	}
}

func runDeviceStateList(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	diagnosticsService, err := diagnostics.New(device)
	if err != nil {
		return fmt.Errorf("error creating diagnostics service: %w", err)
	}
	defer diagnosticsService.Close()

	profiles, err := diagnosticsService.AllValues()
	if err != nil {
		return fmt.Errorf("error listing device state profiles: %w", err)
	}

	return helpers.PrintJSON(profiles, opts)
}

func runDeviceStateEnable(opts *options.Options, profileTypeId, profileId string) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	// This feature requires more complex implementation
	// For now, just return an informational message
	fmt.Printf("Device state profile enabling is not yet implemented for %s:%s\n", profileTypeId, profileId)
	fmt.Printf("Device: %s\n", device.Properties.SerialNumber)
	return nil
}
