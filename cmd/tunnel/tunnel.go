package tunnel

import (
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/cmd/tunnel/list"
	"github.com/danielpaulus/go-ios/cmd/tunnel/start"
	"github.com/danielpaulus/go-ios/cmd/tunnel/stopAgent"
)

// NewTunnelCmd creates a new tunnel command
func NewTunnelCmd(opts *options.Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "tunnel",
		Short: "Manage tunnels",
		Long:  `Create and manage tunnels to iOS devices.`,
	}

	// Add subcommands
	cmd.AddCommand(list.NewListCmd(opts))
	cmd.AddCommand(start.NewStartCmd(opts))
	cmd.AddCommand(stopAgent.NewStopAgentCmd())

	return cmd
}
