package stopAgent

import (
	"fmt"
	"log/slog"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/ios/tunnel"
)

func NewStopAgentCmd() *cobra.Command {
	return &cobra.Command{
		Use:   "stop-agent",
		Short: "Stop tunnel agent",
		Long:  `Stop the tunnel agent.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			err := tunnel.CloseAgent()
			if err != nil {
				return fmt.Errorf("error closing agent: %w", err)
			}
			slog.Info("Tunnel agent stopped")
			return nil
		},
	}
}
