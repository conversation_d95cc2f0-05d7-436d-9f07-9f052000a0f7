package start

import (
	"context"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/tunnel"
)

const defaultPairRecordPath = "/var/db/lockdown/RemotePairing/user_501"

type Options struct {
	*options.Options

	PairRecordPath string
	Userspace      bool
}

func NewStartCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "start",
		Short: "Start a tunnel",
		Long:  `Creates a tunnel connection to the device. If the device was not paired with the host yet, device pairing will also be executed.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			if !o.Userspace {
				err := ios.CheckRoot()
				if err != nil {
					return fmt.Errorf("if --userspace is not set, we need sudo or an admin shell on Windows: %w", err)
				}
			} else {
				slog.Info("using userspace networking")
			}

			if o.PairRecordPath == "" {
				o.PairRecordPath = "."
			}
			if strings.ToLower(o.PairRecordPath) == "default" {
				o.PairRecordPath = defaultPairRecordPath
			}

			return startTunnel(context.Background(), o)
		},
	}

	cmd.Flags().StringVar(&o.PairRecordPath, "pair-record-path", ".", "Path to store pair records")
	cmd.Flags().BoolVar(&o.Userspace, "userspace", false, "Use userspace networking instead of TUN devices")

	return cmd
}

func startTunnel(ctx context.Context, opts *Options) error {
	pm, err := tunnel.NewPairRecordManager(opts.PairRecordPath)
	if err != nil {
		return fmt.Errorf("error creating pair record manager: %w", err)
	}

	tm := tunnel.NewTunnelManager(pm, opts.Userspace)

	go func() {
		ticker := time.NewTicker(1 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				err := tm.UpdateTunnels(ctx)
				if err != nil {
					slog.Warn("failed to update tunnels", "err", err)
				}
			}
		}
	}()

	go func() {
		err := tunnel.ServeTunnelInfo(tm, opts.TunnelInfoHost, opts.TunnelInfoPort)
		if err != nil {
			slog.Error("failed to start tunnel server", "err", err)
		}
	}()

	slog.Info("Tunnel server has been started. Press Ctrl+C to stop.")

	// Wait for context cancellation or signal
	<-ctx.Done()
	return nil
}
