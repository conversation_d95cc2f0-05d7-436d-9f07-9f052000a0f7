package start

import (
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/tunnel"
)

const defaultPairRecordPath = "/var/db/lockdown/RemotePairing/user_501"

type Options struct {
	*options.Options

	PairRecordPath string
	Userspace      bool
}

func NewStartCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "start",
		Short: "Start a tunnel agent",
		Long:  `Creates a tunnel agent that manages the connection to the device. If the device was not paired with the host yet, device pairing will also be executed.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return tunnel.RunAgent(o.TunnelInfoHost, o.TunnelInfoPort, o.PairRecordPath, o.Userspace)
		},
	}

	cmd.Flags().StringVar(&o.PairRecordPath, "pair-record-path", ".", "Path to store pair records")
	cmd.Flags().BoolVar(&o.Userspace, "userspace", false, "Use userspace networking instead of TUN devices")

	return cmd
}
