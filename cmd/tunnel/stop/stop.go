package stop

import (
	"fmt"
	"log/slog"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/tunnel"
)

func NewStopCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "stop",
		Short: "Stop the tunnel agent",
		Long:  `Stop the tunnel agent.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			err := tunnel.CloseAgent(opts.TunnelInfoHost, opts.TunnelInfoPort)
			if err != nil {
				return fmt.Errorf("error closing agent: %w", err)
			}
			slog.Info("Tunnel agent stopped")
			return nil
		},
	}
}
