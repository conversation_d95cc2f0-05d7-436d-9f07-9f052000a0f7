package list

import (
	"encoding/json"
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/tunnel"
)

func NewListCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "list",
		Short: "List tunnels",
		Long:  `List currently started tunnels.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			tunnels, err := tunnel.ListRunningTunnels(opts.TunnelInfoHost, opts.TunnelInfoPort)
			if err != nil {
				return fmt.Errorf("error getting tunnel info: %w", err)
			}

			enc := json.NewEncoder(os.Stdout)
			enc.SetIndent("", "  ")
			return enc.Encode(tunnels)
		},
	}
}
