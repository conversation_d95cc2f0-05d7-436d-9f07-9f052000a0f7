package resetax

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

// NewResetAxCmd creates a new reset-ax command
func NewResetAxCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "reset-ax",
		Short: "Reset accessibility settings",
		Long:  `Reset accessibility settings to defaults.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runResetAx(opts)
		},
	}
}

func runResetAx(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	// Reset accessibility settings by disabling them
	err = ios.SetAssistiveTouch(device, false)
	if err != nil {
		fmt.Printf("Warning: could not reset AssistiveTouch: %v\n", err)
	}

	err = ios.SetVoiceOver(device, false)
	if err != nil {
		fmt.Printf("Warning: could not reset VoiceOver: %v\n", err)
	}

	err = ios.SetZoomTouch(device, false)
	if err != nil {
		fmt.Printf("Warning: could not reset ZoomTouch: %v\n", err)
	}

	fmt.Println("Accessibility settings reset to defaults")
	return nil
}
