package reboot

import (
	"fmt"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/diagnostics"
)

// NewRebootCmd creates a new reboot command
func NewRebootCmd(opts *options.Options) *cobra.Command {
	rebootCmd := &cobra.Command{
		Use:   "reboot",
		Short: "Reboot the device",
		Long:  `Reboot the given device.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			err = diagnostics.Reboot(dev)
			if err != nil {
				return fmt.Errorf("error rebooting device: %w", err)
			}

			log.Info("Device reboot command sent successfully")
			return nil
		},
	}

	return rebootCmd
}
