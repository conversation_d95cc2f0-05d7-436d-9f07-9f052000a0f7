package screenshot

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

type Options struct {
	*options.Options

	Output string
	Stream bool
	Port   int
}

// NewScreenshotCmd creates a new screenshot command
func NewScreenshotCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "screenshot",
		Short: "Take a screenshot",
		Long:  `Takes a screenshot and writes it to the current dir or to <outfile>. If --stream is supplied it starts an mjpeg server at 0.0.0.0:3333. Use --port to set another port.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			if o.Stream {
				return startMJPEGStream(dev, o)
			}

			return saveScreenshot(dev, o)
		},
	}

	cmd.Flags().StringVar(&o.Output, "output", "", "Output file path")
	cmd.Flags().BoolVar(&o.Stream, "stream", false, "Start an MJPEG streaming server")
	cmd.Flags().IntVar(&o.Port, "port", 3333, "Port for the MJPEG streaming server")
	return cmd
}

func startMJPEGStream(device ios.DeviceEntry, opts *Options) error {
	err := instruments.StartMJPEGStreamingServer(device, opts.Port)
	if err != nil {
		return fmt.Errorf("error starting MJPEG streaming server: %w", err)
	}
	return nil
}

func saveScreenshot(device ios.DeviceEntry, opts *Options) error {
	screenshotService, err := instruments.NewScreenshotService(device)
	if err != nil {
		return fmt.Errorf("error creating screenshot service: %w", err)
	}
	defer screenshotService.Close()

	screenshotBytes, err := screenshotService.TakeScreenshot()
	if err != nil {
		return fmt.Errorf("error taking screenshot: %w", err)
	}

	path := opts.Output
	if path == "" {
		path = fmt.Sprintf("screenshot-%s.png", time.Now().Format("20060102-150405"))
	}

	// Ensure the directory exists
	dir := filepath.Dir(path)
	if dir != "." {
		if err := os.MkdirAll(dir, 0o755); err != nil {
			return fmt.Errorf("error creating directory: %w", err)
		}
	}

	if err := os.WriteFile(path, screenshotBytes, 0o600); err != nil {
		return fmt.Errorf("error writing screenshot: %w", err)
	}

	log.Infof("Screenshot saved to %s", path)
	return nil
}
