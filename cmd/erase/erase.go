package erase

import (
	"bufio"
	"fmt"
	"os"
	"strings"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/mcinstall"
)

type Options struct {
	*options.Options
	Force bool
}

// NewEraseCmd creates a new erase command
func NewEraseCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "erase",
		Short: "Erase the device",
		Long:  `Erase the device. It will prompt you to input y+Enter unless --force is specified.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runErase(o)
		},
	}

	cmd.Flags().BoolVar(&o.Force, "force", false, "Force erase without confirmation")

	return cmd
}

func runErase(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	if !opts.Force {
		log.Warnf("Are you sure you want to erase device %s? (y/n)", device.Properties.SerialNumber)
		reader := bufio.NewReader(os.Stdin)
		input, err := reader.ReadString('\n')
		if err != nil {
			return fmt.Errorf("error reading input: %w", err)
		}

		if !strings.HasPrefix(strings.ToLower(strings.TrimSpace(input)), "y") {
			return fmt.Errorf("operation aborted")
		}
	}

	err = mcinstall.Erase(device)
	if err != nil {
		return fmt.Errorf("error erasing device: %w", err)
	}

	result := map[string]any{
		"status":  "ok",
		"message": "Device erased successfully",
	}

	return helpers.PrintJSON(result, opts.Options)
}
