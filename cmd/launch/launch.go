package launch

import (
	"fmt"
	"os"
	"os/signal"
	"strings"
	"syscall"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

type Options struct {
	*options.Options

	Wait         bool
	KillExisting bool
	Args         []string
	Env          []string
}

// NewLaunchCmd creates a new launch command
func NewLaunchCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "launch <bundleID>",
		Short: "Launch an app",
		Long:  `Launch app with the bundleID on the device. Get your bundle ID from the apps command. --wait keeps the connection open if you want logs.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			bundleID := args[0]

			dev, err := helpers.GetDevice(o.Options)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return launchApp(dev, bundleID, o.Wait, o.KillExisting, o.Args, o.Env)
		},
	}

	// Add flags
	cmd.Flags().BoolVar(&o.Wait, "wait", false, "Keep the connection open if you want logs")
	cmd.Flags().BoolVar(&o.KillExisting, "kill-existing", false, "Kill existing instance of the app before launching")
	cmd.Flags().StringArrayVar(&o.Args, "arg", []string{}, "Arguments to pass to the app")
	cmd.Flags().StringArrayVar(&o.Env, "env", []string{}, "Environment variables to set for the app")

	return cmd
}

func launchApp(device ios.DeviceEntry, bundleID string, wait, killExisting bool, appArgs, envVars []string) error {
	pControl, err := instruments.NewProcessControl(device)
	if err != nil {
		return fmt.Errorf("error creating process control: %w", err)
	}

	opts := map[string]any{}
	if killExisting {
		opts["KillExisting"] = 1
	}

	args := toArgs(appArgs)
	envs := toEnvs(envVars)

	pid, err := pControl.LaunchAppWithArgs(bundleID, args, envs, opts)
	if err != nil {
		return fmt.Errorf("error launching app: %w", err)
	}

	log.WithFields(log.Fields{"pid": pid}).Info("Process launched")

	if wait {
		c := make(chan os.Signal, 1)
		signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)
		<-c
		log.WithFields(log.Fields{"pid": pid}).Info("Stop listening to logs")
	}

	return nil
}

// toArgs converts a slice of strings to a slice of any
func toArgs(argsIn []string) []any {
	args := []any{}
	for _, arg := range argsIn {
		args = append(args, arg)
	}
	return args
}

// toEnvs converts a slice of strings to a map of environment variables
func toEnvs(envs []string) map[string]any {
	result := make(map[string]any)
	for _, env := range envs {
		parts := strings.SplitN(env, "=", 2)
		if len(parts) == 2 {
			result[parts[0]] = parts[1]
		}
	}
	return result
}
