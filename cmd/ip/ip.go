package ip

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/pcap"
)

// NewIpCmd creates a new ip command
func NewIpCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "ip",
		Short: "Find device IP address",
		Long: `Uses the live pcap iOS packet capture to wait until it finds one that contains the IP address of the device.
It relies on the MAC address of the WiFi adapter to know which is the right IP.
You have to disable the "automatic wifi address"-privacy feature of the device for this to work.
If you wanna speed it up, open apple maps or similar to force network traffic.
f.ex. "ios launch com.apple.Maps"`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runIp(opts)
		},
	}
}

func runIp(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	ip, err := pcap.FindIp(device)
	if err != nil {
		return fmt.Errorf("error finding IP address: %w", err)
	}

	result := map[string]any{
		"ip_address": ip,
	}

	return helpers.PrintJSON(result, opts)
}
