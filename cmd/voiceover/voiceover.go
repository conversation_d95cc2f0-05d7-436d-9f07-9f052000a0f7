package voiceover

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

type Options struct {
	*options.Options
	Force bool
}

// NewVoiceOverCmd creates a new voiceover command
func NewVoiceOverCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "voiceover",
		Short: "Control VoiceOver feature",
		Long:  `Enables, disables, toggles, or returns the state of the "VoiceOver" software home-screen button. iOS 11+ only (Use --force to try on older versions).`,
	}

	// Add subcommands
	cmd.AddCommand(newEnableCmd(o))
	cmd.AddCommand(newDisableCmd(o))
	cmd.AddCommand(newToggleCmd(o))
	cmd.AddCommand(newGetCmd(o))

	cmd.PersistentFlags().BoolVar(&o.Force, "force", false, "Force operation on older iOS versions")

	return cmd
}

func newEnableCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "enable",
		Short: "Enable VoiceOver",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runVoiceOver(opts, "enable")
		},
	}
}

func newDisableCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "disable",
		Short: "Disable VoiceOver",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runVoiceOver(opts, "disable")
		},
	}
}

func newToggleCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "toggle",
		Short: "Toggle VoiceOver",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runVoiceOver(opts, "toggle")
		},
	}
}

func newGetCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "get",
		Short: "Get VoiceOver state",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runVoiceOver(opts, "get")
		},
	}
}

func runVoiceOver(opts *Options, action string) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	switch action {
	case "enable":
		err = ios.SetVoiceOver(device, true)
		if err != nil {
			return fmt.Errorf("error enabling VoiceOver: %w", err)
		}
		fmt.Println("VoiceOver enabled")

	case "disable":
		err = ios.SetVoiceOver(device, false)
		if err != nil {
			return fmt.Errorf("error disabling VoiceOver: %w", err)
		}
		fmt.Println("VoiceOver disabled")

	case "toggle":
		current, err := ios.GetVoiceOver(device)
		if err != nil {
			return fmt.Errorf("error getting current VoiceOver state: %w", err)
		}

		err = ios.SetVoiceOver(device, !current)
		if err != nil {
			return fmt.Errorf("error toggling VoiceOver: %w", err)
		}

		if current {
			fmt.Println("VoiceOver disabled")
		} else {
			fmt.Println("VoiceOver enabled")
		}

	case "get":
		state, err := ios.GetVoiceOver(device)
		if err != nil {
			return fmt.Errorf("error getting VoiceOver state: %w", err)
		}

		result := map[string]any{
			"voiceover_enabled": state,
		}

		return helpers.PrintJSON(result, opts.Options)

	default:
		return fmt.Errorf("unknown action: %s", action)
	}

	return nil
}
