package crash

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/crashreport"
)

// NewCrashCmd creates a new crash command
func NewCrashCmd(opts *options.Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "crash",
		Short: "Manage crash reports",
		Long:  `Manage crash reports on the device.`,
	}

	// Add subcommands
	cmd.AddCommand(newLsCmd(opts))
	cmd.AddCommand(newCpCmd(opts))
	cmd.AddCommand(newRmCmd(opts))

	return cmd
}

func newLsCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "ls [pattern]",
		Short: "List crash reports",
		Long:  `List crash reports. Use a pattern like '*ips*' to filter.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			pattern := "*"
			if len(args) > 0 {
				pattern = args[0]
			}
			return runCrashLs(opts, pattern)
		},
	}
}

func newCpCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "cp <srcpattern> <target>",
		Short: "Copy crash reports",
		Long:  `Copy crash reports matching pattern to target directory. Ex.: 'ios crash cp "*" "./crashes"'`,
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runCrashCp(opts, args[0], args[1])
		},
	}
}

func newRmCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "rm <cwd> <pattern>",
		Short: "Remove crash reports",
		Long:  `Remove crash reports matching pattern from directory. Ex.: 'ios crash rm "." "*"' to delete everything`,
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runCrashRm(opts, args[0], args[1])
		},
	}
}

func runCrashLs(opts *options.Options, pattern string) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	files, err := crashreport.ListReports(device, pattern)
	if err != nil {
		return fmt.Errorf("error listing crash reports: %w", err)
	}

	result := map[string]any{
		"files":  files,
		"length": len(files),
	}

	return helpers.PrintJSON(result, opts)
}

func runCrashCp(opts *options.Options, srcPattern, target string) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	err = crashreport.DownloadReports(device, srcPattern, target)
	if err != nil {
		return fmt.Errorf("error downloading crash reports: %w", err)
	}

	fmt.Printf("Crash reports copied to %s\n", target)
	return nil
}

func runCrashRm(opts *options.Options, cwd, pattern string) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	err = crashreport.RemoveReports(device, cwd, pattern)
	if err != nil {
		return fmt.Errorf("error removing crash reports: %w", err)
	}

	fmt.Printf("Crash reports matching '%s' removed from %s\n", pattern, cwd)
	return nil
}
