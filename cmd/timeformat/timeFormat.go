package timeformat

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

type Options struct {
	*options.Options
	Force bool
}

// NewTimeFormatCmd creates a new time-format command
func NewTimeFormatCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "time-format",
		Short: "Control time format setting",
		Long:  `Sets, or returns the state of the "time format". iOS 11+ only (Use --force to try on older versions).`,
	}

	// Add subcommands
	cmd.AddCommand(new24hCmd(o))
	cmd.AddCommand(new12hCmd(o))
	cmd.AddCommand(newToggleCmd(o))
	cmd.AddCommand(newGetCmd(o))

	cmd.PersistentFlags().BoolVar(&o.Force, "force", false, "Force operation on older iOS versions")

	return cmd
}

func new24hCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "24h",
		Short: "Set 24-hour time format",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runTimeFormat(opts, "24h")
		},
	}
}

func new12hCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "12h",
		Short: "Set 12-hour time format",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runTimeFormat(opts, "12h")
		},
	}
}

func newToggleCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "toggle",
		Short: "Toggle time format",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runTimeFormat(opts, "toggle")
		},
	}
}

func newGetCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "get",
		Short: "Get current time format",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runTimeFormat(opts, "get")
		},
	}
}

func runTimeFormat(opts *Options, action string) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	switch action {
	case "24h":
		err = ios.SetUses24HourClock(device, true)
		if err != nil {
			return fmt.Errorf("error setting 24-hour format: %w", err)
		}
		fmt.Println("Time format set to 24-hour")

	case "12h":
		err = ios.SetUses24HourClock(device, false)
		if err != nil {
			return fmt.Errorf("error setting 12-hour format: %w", err)
		}
		fmt.Println("Time format set to 12-hour")

	case "toggle":
		current, err := ios.GetUses24HourClock(device)
		if err != nil {
			return fmt.Errorf("error getting current time format: %w", err)
		}

		err = ios.SetUses24HourClock(device, !current)
		if err != nil {
			return fmt.Errorf("error toggling time format: %w", err)
		}

		if current {
			fmt.Println("Time format changed to 12-hour")
		} else {
			fmt.Println("Time format changed to 24-hour")
		}

	case "get":
		is24h, err := ios.GetUses24HourClock(device)
		if err != nil {
			return fmt.Errorf("error getting time format: %w", err)
		}

		result := map[string]any{
			"24_hour_format": is24h,
			"format": func() string {
				if is24h {
					return "24h"
				}
				return "12h"
			}(),
		}

		return helpers.PrintJSON(result, opts.Options)

	default:
		return fmt.Errorf("unknown action: %s", action)
	}

	return nil
}
