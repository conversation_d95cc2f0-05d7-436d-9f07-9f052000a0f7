package options

type Options struct {
	<PERSON>SO<PERSON>isabled   bool   // disable JSON output
	PrettyJSON     bool   // pretty-print JSON output
	Verbose        bool   // enable verbose logging
	Trace          bool   // enable trace logging
	UDID           string // UDID of the device
	TunnelInfoHost string // host for tunnel info API
	TunnelInfoPort int    // port for tunnel info API
	Address        string // address of the device
	RsdPort        int    // port of remote service discovery
	ProxyURL       string // proxy URL
	UserspaceHost  string // host for userspace tunnel
	UserspacePort  int    // port for userspace tunnel
}
