package ps

import (
	"fmt"
	"sort"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/installationproxy"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

type Options struct {
	*options.Options

	Apps bool
}

// NewPsCmd creates a new ps command
func NewPsCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "ps",
		Short: "List running processes",
		Long:  `Dumps a list of running processes on the device. Use --nojson for a human-readable listing including BundleID when available. --apps limits output to processes flagged by iOS as "isApplication".`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return processList(dev, o)
		},
	}

	// Add flags
	cmd.Flags().BoolVar(&o.Apps, "apps", false, "Limit output to processes flagged by iOS as 'isApplication'")

	return cmd
}

func processList(device ios.DeviceEntry, opts *Options) error {
	svc, err := instruments.NewDeviceInfoService(device)
	if err != nil {
		return fmt.Errorf("error creating device info service: %w", err)
	}
	defer svc.Close()

	processes, err := svc.ProcessList()
	if err != nil {
		return fmt.Errorf("error getting process list: %w", err)
	}

	if opts.Apps {
		var filteredProcesses []instruments.ProcessInfo
		for _, process := range processes {
			if process.IsApplication {
				filteredProcesses = append(filteredProcesses, process)
			}
		}
		processes = filteredProcesses
	}

	if !opts.JSONDisabled {
		s, err := helpers.ConvertToJSONString(processes, opts.PrettyJSON)
		if err != nil {
			return err
		}

		fmt.Println(s)
		return nil
	}

	// Get app info for mapping executable names to bundle IDs
	appInfoByExecutableName := make(map[string]installationproxy.AppInfo)
	appSvc, err := installationproxy.New(device)
	if err == nil {
		apps, err := appSvc.BrowseAllApps()
		if err == nil {
			for _, app := range apps {
				if app.CFBundleExecutable != "" {
					appInfoByExecutableName[app.CFBundleExecutable] = app
				}
			}
		}
	}

	// Sort processes by PID
	sort.Slice(
		processes, func(i, j int) bool {
			return processes[i].Pid < processes[j].Pid
		},
	)

	// Find the maximum PID and name length for formatting
	maxPid := uint64(0)
	maxNameLength := 0
	for _, processInfo := range processes {
		if processInfo.Pid > maxPid {
			maxPid = processInfo.Pid
		}
		if len(processInfo.Name) > maxNameLength {
			maxNameLength = len(processInfo.Name)
		}
	}

	maxPidLength := len(fmt.Sprintf("%d", maxPid))

	fmt.Printf("%*s %-*s %s  %s\n", maxPidLength, "PID", maxNameLength, "NAME", "START_DATE         ", "BUNDLE_ID")
	for _, processInfo := range processes {
		bundleID := ""
		appInfo, exists := appInfoByExecutableName[processInfo.Name]
		if exists {
			bundleID = appInfo.CFBundleIdentifier
		}
		fmt.Printf(
			"%*d %-*s %s  %s\n", maxPidLength, processInfo.Pid, maxNameLength, processInfo.Name,
			processInfo.StartDate.Format("2006-01-02 15:04:05"), bundleID,
		)
	}

	return nil
}
