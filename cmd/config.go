package cmd

import (
	"log/slog"
	"os"

	"github.com/spf13/viper"

	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/tunnel"
)

func initConfig(opts *options.Options) {
	// Update options with values from viper (which includes environment variables)
	updateOptionsFromViper(opts)

	// Set up JSON formatting and logging level
	var level slog.Level
	if opts.Trace {
		slog.Info("Set Trace mode")
		level = slog.LevelDebug // slog doesn't have trace, use debug
	} else if opts.Verbose {
		slog.Info("Set Debug mode")
		level = slog.LevelDebug
	} else {
		level = slog.LevelInfo
	}

	// Configure slog handler based on JSON preference
	var handler slog.Handler
	if opts.JSONDisabled {
		handler = slog.NewTextHandler(os.Stderr, &slog.HandlerOptions{Level: level})
	} else {
		handler = slog.NewJSONHandler(os.Stderr, &slog.HandlerOptions{Level: level})
	}
	slog.SetDefault(slog.New(handler))

	// Check if the agent is running
	skipAgent, _ := os.LookupEnv("ENABLE_GO_IOS_AGENT")
	if skipAgent == "user" || skipAgent == "kernel" {
		go func() {
			if err := tunnel.RunAgent(opts.TunnelInfoHost, opts.TunnelInfoPort, "", skipAgent == "user"); err != nil {
				slog.Error("Failed to run agent", "error", err)
			}
		}()
		tunnel.WaitUntilAgentReady(opts.TunnelInfoHost, opts.TunnelInfoPort)
	}

	if !tunnel.IsAgentRunning(opts.TunnelInfoHost, opts.TunnelInfoPort) {
		slog.Warn("go-ios agent is not running. You might need to start it with 'ios tunnel start' for ios17+. Use ENABLE_GO_IOS_AGENT=user for userspace tunnel or ENABLE_GO_IOS_AGENT=kernel for kernel tunnel for the experimental daemon mode.")
	}

	// Set up proxy URL if provided
	if opts.ProxyURL != "" {
		if err := ios.UseHttpProxy(opts.ProxyURL); err != nil {
			slog.Error("Could not parse proxy url", "error", err)
		}
	}
}

// updateOptionsFromViper updates the options struct with values from viper
// This allows environment variables to override default values when command line flags are not set
func updateOptionsFromViper(opts *options.Options) {
	// Update TunnelInfoHost if not set via command line but available in viper (env var)
	if opts.TunnelInfoHost == "" {
		if host := viper.GetString(options.FlagOfTunnelInfoHost); host != "" {
			opts.TunnelInfoHost = host
		} else {
			// Use default if neither command line nor env var is set
			opts.TunnelInfoHost = ios.DefaultAgentHost
		}
	}

	// Update TunnelInfoPort if not set via command line but available in viper (env var)
	if opts.TunnelInfoPort == 0 {
		if port := viper.GetInt(options.FlagOfTunnelInfoPort); port != 0 {
			opts.TunnelInfoPort = port
		} else {
			// Use default if neither command line nor env var is set
			opts.TunnelInfoPort = ios.DefaultAgentPort
		}
	}

	// Update UserspaceHost if not set via command line but available in viper (env var)
	if opts.UserspaceHost == "" {
		if host := viper.GetString(options.FlagOfUserspaceHost); host != "" {
			opts.UserspaceHost = host
		} else {
			// Use default if neither command line nor env var is set
			opts.UserspaceHost = ios.DefaultAgentHost
		}
	}
}
