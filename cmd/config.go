package cmd

import (
	"log/slog"
	"os"

	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/tunnel"
)

func initConfig(opts *options.Options) {
	// Set up JSON formatting and logging level
	var level slog.Level
	if opts.Trace {
		slog.Info("Set Trace mode")
		level = slog.LevelDebug // slog doesn't have trace, use debug
	} else if opts.Verbose {
		slog.Info("Set Debug mode")
		level = slog.LevelDebug
	} else {
		level = slog.LevelInfo
	}

	// Configure slog handler based on JSON preference
	var handler slog.Handler
	if opts.JSONDisabled {
		handler = slog.NewTextHandler(os.Stderr, &slog.HandlerOptions{Level: level})
	} else {
		handler = slog.NewJSONHandler(os.Stderr, &slog.HandlerOptions{Level: level})
	}
	slog.SetDefault(slog.New(handler))

	// Check if the agent is running
	skipAgent, _ := os.LookupEnv("ENABLE_GO_IOS_AGENT")
	if skipAgent == "user" || skipAgent == "kernel" {
		if err := tunnel.RunAgent(skipAgent); err != nil {
			slog.Error("Failed to run agent", "error", err)
		}
	}

	if !tunnel.IsAgentRunning() {
		slog.Warn("go-ios agent is not running. You might need to start it with 'ios tunnel start' for ios17+. Use ENABLE_GO_IOS_AGENT=user for userspace tunnel or ENABLE_GO_IOS_AGENT=kernel for kernel tunnel for the experimental daemon mode.")
	}

	// Set up proxy URL if provided
	if opts.ProxyURL != "" {
		if err := ios.UseHttpProxy(opts.ProxyURL); err != nil {
			slog.Error("Could not parse proxy url", "error", err)
		}
	}

	viper
}
