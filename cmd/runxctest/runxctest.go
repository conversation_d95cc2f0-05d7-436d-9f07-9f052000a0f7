package runxctest

import (
	"context"
	"fmt"
	"io"
	"os"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/testmanagerd"
)

type Options struct {
	*options.Options

	XCTestRunFilePath string
	LogOutput         string
}

// NewRunXcTestCmd creates a new run-xctest command
func NewRunXcTestCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "run-xctest",
		Short: "Run XCTest",
		Long: `Run a XCTest. The --xctestrun-file-path specifies the path to the .xctestrun file to configure the test execution.
If you provide '-' as log output, it prints results to stdout.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runRunXcTest(o)
		},
	}

	cmd.Flags().StringVar(&o.XCTestRunFilePath, "xctestrun-file-path", "", "Path to .xctestrun file")
	cmd.Flags().StringVar(&o.LogOutput, "log-output", "", "Log output file ('-' for stdout)")

	return cmd
}

func runRunXcTest(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	if opts.XCTestRunFilePath == "" {
		return fmt.Errorf("xctestrun-file-path is required")
	}

	if opts.LogOutput != "" {
		var writer *os.File = os.Stdout
		if opts.LogOutput != "-" {
			file, err := os.Create(opts.LogOutput)
			if err != nil {
				return fmt.Errorf("error creating log file: %w", err)
			}
			writer = file
		}
		defer writer.Close()

		listener := testmanagerd.NewTestListener(writer, writer, os.TempDir())

		testResults, err := testmanagerd.StartXCTestWithConfig(context.TODO(), opts.XCTestRunFilePath, device, listener)
		if err != nil {
			return fmt.Errorf("error running XCTest: %w", err)
		}

		log.Infof("Test results: %+v", testResults)
	} else {
		listener := testmanagerd.NewTestListener(io.Discard, io.Discard, os.TempDir())
		_, err := testmanagerd.StartXCTestWithConfig(context.TODO(), opts.XCTestRunFilePath, device, listener)
		if err != nil {
			return fmt.Errorf("error running XCTest: %w", err)
		}
	}

	return nil
}
