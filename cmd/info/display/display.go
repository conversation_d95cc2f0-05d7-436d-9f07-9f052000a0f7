package display

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/deviceinfo"
)

// NewDisplayInfoCmd creates a new display info command
func NewDisplayInfoCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "display",
		Short: "Print display information",
		Long:  `Prints display information from the device.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return printDisplayInfo(dev, opts)
		},
	}
}

func printDisplayInfo(device ios.DeviceEntry, opts *options.Options) error {
	deviceInfo, err := deviceinfo.NewDeviceInfo(device)
	if err != nil {
		return fmt.Errorf("error connecting to deviceinfo service: %w", err)
	}
	defer func(deviceInfo *deviceinfo.Connection) {
		if deviceInfo != nil {
			_ = deviceInfo.Close()
		}
	}(deviceInfo)

	info, err := deviceInfo.GetDisplayInfo()
	if err != nil {
		return fmt.Errorf("error fetching display info: %w", err)
	}

	if opts.JSONDisabled {
		fmt.Printf("display info: %v\n", info)
		return nil
	}

	s, err := helpers.ConvertToJSONString(info, opts.PrettyJSON)
	if err != nil {
		return err
	}

	fmt.Println(s)
	return nil
}
