package info

import (
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/info/display"
	"github.com/danielpaulus/go-ios/cmd/info/lockdown"
	"github.com/danielpaulus/go-ios/cmd/options"
)

// NewInfoCmd creates a new info command
func NewInfoCmd(opts *options.Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "info",
		Short: "Print device information",
		Long:  `Prints a dump of device information from the given source.`,
	}

	// Add subcommands
	cmd.AddCommand(display.NewDisplayInfoCmd(opts))
	cmd.AddCommand(lockdown.NewLockdownInfoCmd(opts))

	return cmd
}
