package lockdown

import (
	"fmt"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

func NewLockdownInfoCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "lockdown",
		Short: "Print lockdown information",
		Long:  `Prints lockdown information from the device.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return printLockdownInfo(dev, opts)
		},
	}
}

func printLockdownInfo(device ios.DeviceEntry, opts *options.Options) error {
	values, err := ios.GetValuesPlist(device)
	if err != nil {
		return fmt.Errorf("error getting values: %w", err)
	}

	svc, err := instruments.NewDeviceInfoService(device)
	if err != nil {
		log.Debugf("could not open instruments, probably dev image not mounted %v", err)
	} else {
		defer svc.Close()

		info, err := svc.NetworkInformation()
		if err != nil {
			log.Debugf("error getting networkinfo from instruments %v", err)
		} else {
			values["instruments:networkInformation"] = info
		}
		info, err = svc.HardwareInformation()
		if err != nil {
			log.Debugf("error getting hardwareinfo from instruments %v", err)
		} else {
			values["instruments:hardwareInformation"] = info
		}
	}

	s, err := helpers.ConvertToJSONString(values, opts.PrettyJSON)
	if err != nil {
		return err
	}

	fmt.Println(s)
	return nil
}
