package date

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

// NewDeviceDateCmd creates a new device-date command
func NewDeviceDateCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "device-date",
		Short: "Print device date",
		Long:  `Prints the device date.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return printDeviceDate(dev, opts)
		},
	}
}

func printDeviceDate(device ios.DeviceEntry, opts *options.Options) error {
	lockdown, err := ios.ConnectLockdownWithSession(device)
	if err != nil {
		return fmt.Errorf("error connecting to lockdown: %w", err)
	}
	defer lockdown.Close()

	value, err := lockdown.GetValue("TimeIntervalSince1970")
	if err != nil {
		return fmt.Errorf("error getting device date: %w", err)
	}

	if opts.JSONDisabled {
		fmt.Printf("device date: %v\n", value)
		return nil
	}

	s, err := helpers.ConvertToJSONString(value, opts.PrettyJSON)
	if err != nil {
		return err
	}

	fmt.Println(s)
	return nil
}
