package readpair

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

// NewReadPairCmd creates a new read-pair command
func NewReadPairCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "read-pair",
		Short: "Read pair record",
		Long:  `Dump detailed information about the pairrecord for a device.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return readPair(dev, opts)
		},
	}
}

func readPair(device ios.DeviceEntry, opts *options.Options) error {
	pairRecord, err := ios.ReadPairRecord(device.Properties.SerialNumber)
	if err != nil {
		return fmt.Errorf("error reading pair record: %w", err)
	}

	// Create a map with the pair record information
	pairInfo := map[string]any{
		"DeviceCertificateLength": len(pairRecord.DeviceCertificate),
		"HostCertificateLength":   len(pairRecord.HostCertificate),
		"HostPrivateKeyLength":    len(pairRecord.HostPrivateKey),
		"RootCertificateLength":   len(pairRecord.RootCertificate),
		"RootPrivateKeyLength":    len(pairRecord.RootPrivateKey),
		"SystemBUID":              pairRecord.SystemBUID,
		"HostID":                  pairRecord.HostID,
		"WiFiMACAddress":          pairRecord.WiFiMACAddress,
	}

	s, err := helpers.ConvertToJSONString(pairInfo, opts.PrettyJSON)
	if err != nil {
		return err
	}

	fmt.Println(s)
	return nil
}
