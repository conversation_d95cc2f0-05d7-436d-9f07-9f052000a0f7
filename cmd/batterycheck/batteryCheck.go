package batterycheck

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

// NewBatteryCheckCmd creates a new battery-check command
func NewBatteryCheckCmd(opts *options.Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "battery-check",
		Short: "Print battery information",
		Long:  `Prints battery info.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runBatteryCheck(opts)
		},
	}

	return cmd
}

func runBatteryCheck(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	lockdownConnection, err := ios.ConnectLockdownWithSession(device)
	if err != nil {
		return fmt.Errorf("error connecting to lockdown: %w", err)
	}
	defer lockdownConnection.Close()

	batteryInfo, err := lockdownConnection.GetValue("com.apple.mobile.battery")
	if err != nil {
		return fmt.Errorf("error getting battery info: %w", err)
	}

	return helpers.PrintJSON(batteryInfo, opts)
}
