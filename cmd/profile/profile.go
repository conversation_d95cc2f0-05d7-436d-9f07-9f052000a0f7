package profile

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/mcinstall"
)

type Options struct {
	*options.Options

	P12File     string
	P12Password string
}

// NewProfileCmd creates a new profile command
func NewProfileCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "profile",
		Short: "Manage configuration profiles",
		Long:  `Manage configuration profiles on the device.`,
	}

	// Add subcommands
	cmd.AddCommand(newListCmd(o))
	cmd.AddCommand(newAddCmd(o))
	cmd.AddCommand(newRemoveCmd(o))

	cmd.PersistentFlags().StringVar(&o.P12File, "p12file", "", "P12 certificate file for supervised devices")
	cmd.PersistentFlags().StringVar(&o.P12Password, "password", "", "P12 certificate password (or use P12_PASSWORD env var)")

	return cmd
}

func newListCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "list",
		Short: "List profiles on device",
		Long:  `List the profiles on the device.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runProfileList(opts)
		},
	}
}

func newAddCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "add <profileFile>",
		Short: "Install profile on device",
		Long:  `Install profile file on the device. If supervised set p12file and password or the environment variable 'P12_PASSWORD'.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runProfileAdd(opts, args[0])
		},
	}
}

func newRemoveCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "remove <profileName>",
		Short: "Remove profile from device",
		Long:  `Remove the profileName from the device.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			return runProfileRemove(opts, args[0])
		},
	}
}

func runProfileList(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	profileService, err := mcinstall.New(device)
	if err != nil {
		return fmt.Errorf("error creating profile service: %w", err)
	}
	defer profileService.Close()

	profiles, err := profileService.HandleList()
	if err != nil {
		return fmt.Errorf("error listing profiles: %w", err)
	}

	return helpers.PrintJSON(profiles, opts.Options)
}

func runProfileAdd(opts *Options, profileFile string) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	profileData, err := os.ReadFile(profileFile)
	if err != nil {
		return fmt.Errorf("error reading profile file: %w", err)
	}

	if opts.P12Password == "" {
		opts.P12Password = os.Getenv("P12_PASSWORD")
	}

	profileService, err := mcinstall.New(device)
	if err != nil {
		return fmt.Errorf("error creating profile service: %w", err)
	}
	defer profileService.Close()

	if opts.P12File != "" {
		p12bytes, err := os.ReadFile(opts.P12File)
		if err != nil {
			return fmt.Errorf("error reading P12 file: %w", err)
		}

		err = profileService.AddProfileSupervised(profileData, p12bytes, opts.P12Password)
		if err != nil {
			return fmt.Errorf("error installing profile (supervised): %w", err)
		}
		fmt.Printf("Profile %s installed successfully (supervised)\n", profileFile)
	} else {
		err = profileService.AddProfile(profileData)
		if err != nil {
			return fmt.Errorf("error installing profile: %w", err)
		}
		fmt.Printf("Profile %s installed successfully (you have to accept it in the device settings)\n", profileFile)
	}

	return nil
}

func runProfileRemove(opts *Options, profileName string) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	profileService, err := mcinstall.New(device)
	if err != nil {
		return fmt.Errorf("error creating profile service: %w", err)
	}
	defer profileService.Close()

	err = profileService.RemoveProfile(profileName)
	if err != nil {
		return fmt.Errorf("error removing profile: %w", err)
	}

	fmt.Printf("Profile %s removed successfully\n", profileName)
	return nil
}
