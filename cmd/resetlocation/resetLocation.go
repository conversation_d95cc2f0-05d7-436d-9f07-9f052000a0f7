package resetlocation

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/simlocation"
)

// NewResetLocationCmd creates a new reset-location command
func NewResetLocationCmd(opts *options.Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "reset-location",
		Short: "Reset device location",
		Long:  `Resets the location of the device to the actual one.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runResetLocation(opts)
		},
	}

	return cmd
}

func runResetLocation(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	err = simlocation.ResetLocation(device)
	if err != nil {
		return fmt.Errorf("error resetting location: %w", err)
	}

	fmt.Println("Device location reset to actual location")
	return nil
}
