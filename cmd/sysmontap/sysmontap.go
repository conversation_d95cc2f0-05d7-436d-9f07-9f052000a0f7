package sysmontap

import (
	"fmt"
	"os"
	"os/signal"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

// NewSysmonTapCmd creates a new sysmontap command
func NewSysmonTapCmd(opts *options.Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "sysmontap",
		Short: "Get system statistics",
		Long:  `Get system stats like MEM, CPU.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runSysmonTap(opts)
		},
	}

	return cmd
}

func runSysmonTap(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	const xcodeDefaultSamplingRate = 10
	sysmon, err := instruments.NewSysmontapService(device, xcodeDefaultSamplingRate)
	if err != nil {
		return fmt.Errorf("error creating system monitor: %w", err)
	}
	defer func(sysmon *instruments.SysmontapService) {
		if sysmon != nil {
			_ = sysmon.Close()
		}
	}(sysmon)

	cpuUsageChannel := sysmon.ReceiveCPUUsage()

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)

	log.Info("Starting to monitor CPU usage... Press CTRL+C to stop.")

	for {
		select {
		case cpuUsageMsg, ok := <-cpuUsageChannel:
			if !ok {
				log.Info("CPU usage channel closed.")
				return nil
			}
			log.WithFields(
				log.Fields{
					"cpu_count":      cpuUsageMsg.CPUCount,
					"enabled_cpus":   cpuUsageMsg.EnabledCPUs,
					"end_time":       cpuUsageMsg.EndMachAbsTime,
					"cpu_total_load": cpuUsageMsg.SystemCPUUsage.CPU_TotalLoad,
				},
			).Info("Received CPU usage data")

		case <-c:
			log.Info("Shutting down sysmontap")
			return nil
		}
	}
}
