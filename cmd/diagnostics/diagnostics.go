package diagnostics

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/diagnostics"
)

// NewDiagnosticsCmd creates a new diagnostics command
func NewDiagnosticsCmd(opts *options.Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "diagnostics",
		Short: "Access diagnostic information",
		Long:  `Access diagnostic information from the device.`,
	}

	// Add subcommands
	cmd.AddCommand(newListCmd(opts))

	return cmd
}

func newListCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "list",
		Short: "List diagnostic information",
		Long:  `List diagnostic infos.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runDiagnosticsList(opts)
		},
	}
}

func runDiagnosticsList(opts *options.Options) error {
	device, err := helpers.GetDevice(opts)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	diagnosticsService, err := diagnostics.New(device)
	if err != nil {
		return fmt.Errorf("error creating diagnostics service: %w", err)
	}
	defer diagnosticsService.Close()

	info, err := diagnosticsService.AllValues()
	if err != nil {
		return fmt.Errorf("error getting diagnostic info: %w", err)
	}

	return helpers.PrintJSON(info, opts)
}
