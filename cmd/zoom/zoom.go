package zoom

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

type Options struct {
	*options.Options
	Force bool
}

// NewZoomCmd creates a new zoom command
func NewZoomCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "zoom",
		Short: "Control ZoomTouch feature",
		Long:  `Enables, disables, toggles, or returns the state of the "ZoomTouch" software home-screen button. iOS 11+ only (Use --force to try on older versions).`,
	}

	// Add subcommands
	cmd.AddCommand(newEnableCmd(o))
	cmd.AddCommand(newDisableCmd(o))
	cmd.AddCommand(newToggleCmd(o))
	cmd.AddCommand(newGetCmd(o))

	cmd.PersistentFlags().BoolVar(&o.Force, "force", false, "Force operation on older iOS versions")

	return cmd
}

func newEnableCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "enable",
		Short: "Enable ZoomTouch",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runZoom(opts, "enable")
		},
	}
}

func newDisableCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "disable",
		Short: "Disable ZoomTouch",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runZoom(opts, "disable")
		},
	}
}

func newToggleCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "toggle",
		Short: "Toggle ZoomTouch",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runZoom(opts, "toggle")
		},
	}
}

func newGetCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "get",
		Short: "Get ZoomTouch state",
		RunE: func(cmd *cobra.Command, args []string) error {
			return runZoom(opts, "get")
		},
	}
}

func runZoom(opts *Options, action string) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	switch action {
	case "enable":
		err = ios.SetZoomTouch(device, true)
		if err != nil {
			return fmt.Errorf("error enabling ZoomTouch: %w", err)
		}
		fmt.Println("ZoomTouch enabled")

	case "disable":
		err = ios.SetZoomTouch(device, false)
		if err != nil {
			return fmt.Errorf("error disabling ZoomTouch: %w", err)
		}
		fmt.Println("ZoomTouch disabled")

	case "toggle":
		current, err := ios.GetZoomTouch(device)
		if err != nil {
			return fmt.Errorf("error getting current ZoomTouch state: %w", err)
		}

		err = ios.SetZoomTouch(device, !current)
		if err != nil {
			return fmt.Errorf("error toggling ZoomTouch: %w", err)
		}

		if current {
			fmt.Println("ZoomTouch disabled")
		} else {
			fmt.Println("ZoomTouch enabled")
		}

	case "get":
		state, err := ios.GetZoomTouch(device)
		if err != nil {
			return fmt.Errorf("error getting ZoomTouch state: %w", err)
		}

		result := map[string]any{
			"zoomtouch_enabled": state,
		}

		return helpers.PrintJSON(result, opts.Options)

	default:
		return fmt.Errorf("unknown action: %s", action)
	}

	return nil
}
