package cmd

import (
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/activate"
	"github.com/danielpaulus/go-ios/cmd/apps"
	"github.com/danielpaulus/go-ios/cmd/assistivetouch"
	"github.com/danielpaulus/go-ios/cmd/ax"
	"github.com/danielpaulus/go-ios/cmd/batterycheck"
	"github.com/danielpaulus/go-ios/cmd/batteryregistry"
	"github.com/danielpaulus/go-ios/cmd/configure"
	"github.com/danielpaulus/go-ios/cmd/crash"
	"github.com/danielpaulus/go-ios/cmd/date"
	"github.com/danielpaulus/go-ios/cmd/debug"
	"github.com/danielpaulus/go-ios/cmd/devicename"
	"github.com/danielpaulus/go-ios/cmd/devicestate"
	"github.com/danielpaulus/go-ios/cmd/devmode"
	"github.com/danielpaulus/go-ios/cmd/diagnostics"
	"github.com/danielpaulus/go-ios/cmd/diskspace"
	"github.com/danielpaulus/go-ios/cmd/dproxy"
	"github.com/danielpaulus/go-ios/cmd/erase"
	"github.com/danielpaulus/go-ios/cmd/forward"
	"github.com/danielpaulus/go-ios/cmd/fsync"
	"github.com/danielpaulus/go-ios/cmd/httpproxy"
	"github.com/danielpaulus/go-ios/cmd/image"
	"github.com/danielpaulus/go-ios/cmd/info"
	"github.com/danielpaulus/go-ios/cmd/install"
	"github.com/danielpaulus/go-ios/cmd/instruments"
	"github.com/danielpaulus/go-ios/cmd/ip"
	"github.com/danielpaulus/go-ios/cmd/kill"
	"github.com/danielpaulus/go-ios/cmd/lang"
	"github.com/danielpaulus/go-ios/cmd/launch"
	"github.com/danielpaulus/go-ios/cmd/list"
	"github.com/danielpaulus/go-ios/cmd/listen"
	"github.com/danielpaulus/go-ios/cmd/memlimitoff"
	"github.com/danielpaulus/go-ios/cmd/mobilegestalt"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/cmd/pair"
	"github.com/danielpaulus/go-ios/cmd/pcap"
	"github.com/danielpaulus/go-ios/cmd/prepare"
	"github.com/danielpaulus/go-ios/cmd/profile"
	"github.com/danielpaulus/go-ios/cmd/ps"
	"github.com/danielpaulus/go-ios/cmd/readpair"
	"github.com/danielpaulus/go-ios/cmd/reboot"
	"github.com/danielpaulus/go-ios/cmd/resetax"
	"github.com/danielpaulus/go-ios/cmd/resetlocation"
	"github.com/danielpaulus/go-ios/cmd/rsd"
	"github.com/danielpaulus/go-ios/cmd/runtest"
	"github.com/danielpaulus/go-ios/cmd/runwda"
	"github.com/danielpaulus/go-ios/cmd/runxctest"
	"github.com/danielpaulus/go-ios/cmd/screenshot"
	"github.com/danielpaulus/go-ios/cmd/setlocation"
	"github.com/danielpaulus/go-ios/cmd/setlocationgpx"
	"github.com/danielpaulus/go-ios/cmd/syslog"
	"github.com/danielpaulus/go-ios/cmd/sysmontap"
	"github.com/danielpaulus/go-ios/cmd/timeformat"
	"github.com/danielpaulus/go-ios/cmd/tunnel"
	"github.com/danielpaulus/go-ios/cmd/uninstall"
	"github.com/danielpaulus/go-ios/cmd/version"
	"github.com/danielpaulus/go-ios/cmd/voiceover"
	"github.com/danielpaulus/go-ios/cmd/zoom"
)

var rootCmd = &cobra.Command{
	Use:   "ios",
	Short: "iOS device management tool",
	Long: `go-ios is a tool for managing iOS devices.
It provides various commands for interacting with iOS devices, such as listing devices,
installing apps, taking screenshots, and more.`,
}

// Execute executes the root command.
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	var opts options.Options
	cobra.OnInitialize(func() { initConfig(&opts) })

	// Global flags
	rootCmd.PersistentFlags().BoolVarP(&opts.Verbose, "verbose", "v", false, "Enable Debug Logging")
	rootCmd.PersistentFlags().BoolVarP(&opts.Trace, "trace", "t", false, "Enable Trace Logging (dump every message)")
	rootCmd.PersistentFlags().BoolVar(&opts.JSONDisabled, "no-json", false, "Disable JSON output")
	rootCmd.PersistentFlags().BoolVar(&opts.PrettyJSON, "pretty", false, "Pretty-print JSON command output")
	rootCmd.PersistentFlags().StringVar(&opts.UDID, "udid", "", "UDID of the device")
	rootCmd.PersistentFlags().StringVar(
		&opts.TunnelInfoHost, "tunnel-info-host", "",
		"When go-ios is used to manage tunnels for iOS 17+ it exposes them on an HTTP-API for localhost (default host: 127.0.0.1)",
	)
	rootCmd.PersistentFlags().IntVar(
		&opts.TunnelInfoPort, "tunnel-info-port", 0,
		"When go-ios is used to manage tunnels for iOS 17+ it exposes them on an HTTP-API for localhost (default port: 28100)",
	)
	rootCmd.PersistentFlags().StringVar(
		&opts.Address, "address", "",
		"Address of the device on the interface. This parameter is optional and can be set if a tunnel created by MacOS needs to be used.",
	)
	rootCmd.PersistentFlags().IntVar(
		&opts.RsdPort, "rsd-port", 0, "Port of remote service discovery on the device through the tunnel",
	)
	rootCmd.PersistentFlags().StringVar(
		&opts.ProxyURL, "proxy-url", "",
		"Set this if you want go-ios to use a http proxy for outgoing requests, like for downloading images or contacting Apple during device activation.",
	)
	rootCmd.PersistentFlags().StringVar(
		&opts.UserspaceHost, "userspace-host", "",
		"Optional. Set this if you run a command supplying rsd-port and address and your device is using userspace tunnel",
	)
	rootCmd.PersistentFlags().IntVar(
		&opts.UserspacePort, "userspace-port", 0,
		"Optional. Set this if you run a command supplying rsd-port and address and your device is using userspace tunnel",
	)

	// Add commands
	rootCmd.AddCommand(activate.NewActivateCmd(&opts))
	rootCmd.AddCommand(apps.NewAppsCmd(&opts))
	rootCmd.AddCommand(assistivetouch.NewAssistiveTouchCmd(&opts))
	rootCmd.AddCommand(ax.NewAxCmd(&opts))
	rootCmd.AddCommand(batterycheck.NewBatteryCheckCmd(&opts))
	rootCmd.AddCommand(batteryregistry.NewBatteryRegistryCmd(&opts))
	rootCmd.AddCommand(configure.NewConfigureCmd())
	rootCmd.AddCommand(crash.NewCrashCmd(&opts))
	rootCmd.AddCommand(date.NewDeviceDateCmd(&opts))
	rootCmd.AddCommand(debug.NewDebugCmd(&opts))
	rootCmd.AddCommand(devicename.NewDeviceNameCmd(&opts))
	rootCmd.AddCommand(devicestate.NewDeviceStateCmd(&opts))
	rootCmd.AddCommand(devmode.NewDevModeCmd(&opts))
	rootCmd.AddCommand(diagnostics.NewDiagnosticsCmd(&opts))
	rootCmd.AddCommand(diskspace.NewDiskSpaceCmd(&opts))
	rootCmd.AddCommand(dproxy.NewDProxyCmd(&opts))
	rootCmd.AddCommand(erase.NewEraseCmd(&opts))
	rootCmd.AddCommand(forward.NewForwardCmd(&opts))
	rootCmd.AddCommand(fsync.NewFSyncCmd(&opts))
	rootCmd.AddCommand(httpproxy.NewHttpProxyCmd(&opts))
	rootCmd.AddCommand(image.NewImageCmd(&opts))
	rootCmd.AddCommand(info.NewInfoCmd(&opts))
	rootCmd.AddCommand(install.NewInstallCmd(&opts))
	rootCmd.AddCommand(instruments.NewInstrumentsCmd(&opts))
	rootCmd.AddCommand(ip.NewIpCmd(&opts))
	rootCmd.AddCommand(kill.NewKillCmd(&opts))
	rootCmd.AddCommand(lang.NewLangCmd(&opts))
	rootCmd.AddCommand(launch.NewLaunchCmd(&opts))
	rootCmd.AddCommand(list.NewListCmd(&opts))
	rootCmd.AddCommand(listen.NewListenCmd())
	rootCmd.AddCommand(memlimitoff.NewMemLimitOffCmd(&opts))
	rootCmd.AddCommand(mobilegestalt.NewMobileGestaltCmd(&opts))
	rootCmd.AddCommand(pair.NewPairCmd(&opts))
	rootCmd.AddCommand(pcap.NewPcapCmd(&opts))
	rootCmd.AddCommand(prepare.NewPrepareCmd(&opts))
	rootCmd.AddCommand(profile.NewProfileCmd(&opts))
	rootCmd.AddCommand(ps.NewPsCmd(&opts))
	rootCmd.AddCommand(readpair.NewReadPairCmd(&opts))
	rootCmd.AddCommand(reboot.NewRebootCmd(&opts))
	rootCmd.AddCommand(resetax.NewResetAxCmd(&opts))
	rootCmd.AddCommand(resetlocation.NewResetLocationCmd(&opts))
	rootCmd.AddCommand(rsd.NewRsdCmd(&opts))
	rootCmd.AddCommand(runtest.NewRunTestCmd(&opts))
	rootCmd.AddCommand(runwda.NewRunWdaCmd(&opts))
	rootCmd.AddCommand(runxctest.NewRunXcTestCmd(&opts))
	rootCmd.AddCommand(screenshot.NewScreenshotCmd(&opts))
	rootCmd.AddCommand(setlocation.NewSetLocationCmd(&opts))
	rootCmd.AddCommand(setlocationgpx.NewSetLocationGpxCmd(&opts))
	rootCmd.AddCommand(syslog.NewSyslogCmd(&opts))
	rootCmd.AddCommand(sysmontap.NewSysmonTapCmd(&opts))
	rootCmd.AddCommand(timeformat.NewTimeFormatCmd(&opts))
	rootCmd.AddCommand(tunnel.NewTunnelCmd(&opts))
	rootCmd.AddCommand(uninstall.NewUninstallCmd(&opts))
	rootCmd.AddCommand(version.NewVersionCmd())
	rootCmd.AddCommand(voiceover.NewVoiceOverCmd(&opts))
	rootCmd.AddCommand(zoom.NewZoomCmd(&opts))
}
