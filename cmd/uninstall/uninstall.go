package uninstall

import (
	"fmt"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/installationproxy"
)

// NewUninstallCmd creates a new uninstallation command
func NewUninstallCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "uninstall <bundleID>",
		Short: "Uninstall an app",
		Long:  `Uninstall an app with the specified bundleID from the device.`,
		Args:  cobra.ExactArgs(1),
		RunE: func(cmd *cobra.Command, args []string) error {
			bundleID := args[0]

			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return uninstallApp(dev, bundleID)
		},
	}
}

func uninstallApp(device ios.DeviceEntry, bundleID string) error {
	log.WithFields(
		log.Fields{"bundleID": bundleID, "device": device.Properties.SerialNumber},
	).Info("uninstalling")

	svc, err := installationproxy.New(device)
	if err != nil {
		return fmt.Errorf("error connecting to installationproxy: %w", err)
	}

	err = svc.Uninstall(bundleID)
	if err != nil {
		return fmt.Errorf("error uninstalling app: %w", err)
	}

	log.Info("Successfully uninstalled app")
	return nil
}
