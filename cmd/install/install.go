package install

import (
	"fmt"
	"log/slog"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/zipconduit"
)

type Options struct {
	*options.Options

	Path string
}

// NewInstallCmd creates a new installation command
func NewInstallCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "install",
		Short: "Install an app",
		Long:  `Specify a .app folder or an installable ipa file that will be installed.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(o.Options)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return installApp(dev, o)
		},
	}

	// Add flags
	cmd.Flags().StringVar(&o.Path, "path", "", "Path to the .ipa file or .app folder to install")
	_ = cmd.MarkFlagRequired("path")

	return cmd
}

func installApp(device ios.DeviceEntry, opts *Options) error {
	slog.Info("installing", "appPath", opts.Path, "device", device.Properties.SerialNumber)

	conn, err := zipconduit.New(device)
	if err != nil {
		return fmt.Errorf("error connecting to zipconduit, dev image installed? %w", err)
	}

	err = conn.SendFile(opts.Path)
	if err != nil {
		return fmt.Errorf("error installing app: %w", err)
	}

	slog.Info("Successfully installed app")
	return nil
}
