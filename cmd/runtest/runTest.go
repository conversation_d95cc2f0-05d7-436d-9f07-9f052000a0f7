package runtest

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/testmanagerd"
)

type Options struct {
	*options.Options

	BundleID           string
	TestRunnerBundleId string
	XctestConfig       string
	LogOutput          string
	XcTest             bool
	TestsToRun         []string
	TestsToSkip        []string
	Env                []string
}

// NewRunTestCmd creates a new runtest command
func NewRunTestCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "runtest",
		Short: "Run XCUITest",
		Long: `Run a XCUITest. If you provide only bundle-id go-ios will try to dynamically create test-runner-bundle-id and xctest-config.
If you provide '-' as log output, it prints results to stdout.
To be able to filter for tests to run or skip, use one argument per test selector.
Example: runtest --test-to-run=(TestTarget.)TestClass/testMethod --test-to-run=(TestTarget.)TestClass/testMethod
(the value for 'TestTarget' is optional)
The method name can also be omitted and in this case all tests of the specified class are run.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runRunTest(o)
		},
	}

	cmd.Flags().StringVar(&o.BundleID, "bundle-id", "", "Bundle ID of the app to test")
	cmd.Flags().StringVar(&o.TestRunnerBundleId, "test-runner-bundle-id", "", "Test runner bundle ID")
	cmd.Flags().StringVar(&o.XctestConfig, "xctest-config", "", "XCTest configuration")
	cmd.Flags().StringVar(&o.LogOutput, "log-output", "", "Log output file ('-' for stdout)")
	cmd.Flags().BoolVar(&o.XcTest, "xctest", false, "Use XCTest instead of XCUITest")
	cmd.Flags().StringSliceVar(&o.TestsToRun, "test-to-run", []string{}, "Tests to run")
	cmd.Flags().StringSliceVar(&o.TestsToSkip, "test-to-skip", []string{}, "Tests to skip")
	cmd.Flags().StringSliceVar(&o.Env, "env", []string{}, "Environment variables (KEY=VALUE)")

	return cmd
}

func runRunTest(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	// Parse environment variables
	env := make(map[string]any)
	for _, envVar := range opts.Env {
		key, value, found := parseKeyValue(envVar, "=")
		if found {
			env[key] = value
		}
	}

	config := testmanagerd.TestConfig{
		BundleId:           opts.BundleID,
		TestRunnerBundleId: opts.TestRunnerBundleId,
		XctestConfigName:   opts.XctestConfig,
		Env:                env,
		TestsToRun:         opts.TestsToRun,
		TestsToSkip:        opts.TestsToSkip,
		XcTest:             opts.XcTest,
		Device:             device,
	}

	if opts.LogOutput != "" {
		var writer *os.File = os.Stdout
		if opts.LogOutput != "-" {
			file, err := os.Create(opts.LogOutput)
			if err != nil {
				return fmt.Errorf("error creating log file: %w", err)
			}
			writer = file
		}
		defer writer.Close()

		config.Listener = testmanagerd.NewTestListener(writer, writer, os.TempDir())

		testResults, err := testmanagerd.RunTestWithConfig(context.TODO(), config)
		if err != nil {
			return fmt.Errorf("error running XCUITest: %w", err)
		}

		slog.Info("Test results", "results", testResults)
	} else {
		config.Listener = testmanagerd.NewTestListener(io.Discard, io.Discard, os.TempDir())
		_, err := testmanagerd.RunTestWithConfig(context.TODO(), config)
		if err != nil {
			return fmt.Errorf("error running XCUITest: %w", err)
		}
	}

	return nil
}

func parseKeyValue(input, separator string) (string, string, bool) {
	for i := 0; i < len(input); i++ {
		if input[i:i+len(separator)] == separator {
			return input[:i], input[i+len(separator):], true
		}
	}
	return "", "", false
}
