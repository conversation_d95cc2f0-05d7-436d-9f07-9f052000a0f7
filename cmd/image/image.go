package image

import (
	"fmt"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/imagemounter"
)

type Options struct {
	*options.Options

	Path    string
	BaseDir string
}

// NewImageCmd creates a new image command
func NewImageCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "image",
		Short: "Manage developer disk images",
		Long:  `Manage developer disk images on the device.`,
	}

	// Add subcommands
	cmd.AddCommand(newListCmd(o))
	cmd.AddCommand(newMountCmd(o))
	cmd.AddCommand(newUnmountCmd(o))
	cmd.AddCommand(newAutoCmd(o))

	return cmd
}

func newListCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "list",
		Short: "List mounted developer images",
		Long:  `List currently mounted developers images' signatures.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runImageList(opts)
		},
	}
}

func newMountCmd(opts *Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "mount",
		Short: "Mount developer disk image",
		Long: `Mount a image from <imagepath>.
For iOS 17+ (personalized developer disk images) <imagepath> must point to the "Restore" directory inside the developer disk.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runImageMount(opts)
		},
	}

	cmd.Flags().StringVar(&opts.Path, "path", "", "Path to developer disk image")
	_ = cmd.MarkFlagRequired("path")

	return cmd
}

func newUnmountCmd(opts *Options) *cobra.Command {
	return &cobra.Command{
		Use:   "unmount",
		Short: "Unmount developer disk image",
		Long:  `Unmount developer disk image.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runImageUnmount(opts)
		},
	}
}

func newAutoCmd(opts *Options) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "auto",
		Short: "Automatically download and mount developer image",
		Long: `Automatically download correct dev image from the internets and mount it.
You can specify a dir where images should be cached.
The default is the current dir.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runImageAuto(opts)
		},
	}

	cmd.Flags().StringVar(&opts.BaseDir, "basedir", "./devimages", "Directory where dev images are stored")

	return cmd
}

func runImageList(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	conn, err := imagemounter.NewImageMounter(device)
	if err != nil {
		return fmt.Errorf("error creating image mounter: %w", err)
	}
	defer conn.Close()

	signatures, err := conn.ListImages()
	if err != nil {
		return fmt.Errorf("error listing images: %w", err)
	}

	if len(signatures) == 0 {
		fmt.Println("No mounted developer images found")
		return nil
	}

	result := map[string]any{
		"mounted_images": signatures,
		"count":          len(signatures),
	}

	return helpers.PrintJSON(result, opts.Options)
}

func runImageMount(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	err = imagemounter.MountImage(device, opts.Path)
	if err != nil {
		return fmt.Errorf("error mounting image: %w", err)
	}

	log.WithFields(log.Fields{
		"image": opts.Path,
		"udid":  device.Properties.SerialNumber,
	}).Info("success mounting image")

	fmt.Printf("Developer image mounted from %s\n", opts.Path)
	return nil
}

func runImageUnmount(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	err = imagemounter.UnmountImage(device)
	if err != nil {
		return fmt.Errorf("error unmounting image: %w", err)
	}

	log.WithFields(log.Fields{
		"udid": device.Properties.SerialNumber,
	}).Info("success unmounting image")

	fmt.Println("Developer image unmounted")
	return nil
}

func runImageAuto(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	path, err := imagemounter.DownloadImageFor(device, opts.BaseDir)
	if err != nil {
		return fmt.Errorf("error downloading image: %w", err)
	}

	log.WithFields(log.Fields{
		"basedir": opts.BaseDir,
		"udid":    device.Properties.SerialNumber,
		"path":    path,
	}).Info("Successfully downloaded image")

	err = imagemounter.MountImage(device, path)
	if err != nil {
		return fmt.Errorf("error mounting image: %w", err)
	}

	log.WithFields(log.Fields{
		"image": path,
		"udid":  device.Properties.SerialNumber,
	}).Info("success mounting image")

	fmt.Printf("Developer image automatically downloaded and mounted from %s\n", path)
	return nil
}
