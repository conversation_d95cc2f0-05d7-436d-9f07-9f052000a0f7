package activate

import (
	"fmt"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/mobileactivation"
)

// NewActivateCmd creates a new activate command
func NewActivateCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "activate",
		Short: "Activate a device",
		Long:  `Activate a device.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			err = mobileactivation.Activate(dev)
			if err != nil {
				return fmt.Errorf("error activating device: %w", err)
			}

			log.Info("device activated successfully")
			return nil
		},
	}
}
