package list

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

type (
	Options struct {
		*options.Options

		Details bool
	}
	detailsEntry struct {
		Udid           string
		ProductName    string
		ProductType    string
		ProductVersion string
	}
)

// NewListCmd creates a new list command
func NewListCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "list",
		Short: "List connected devices",
		Long:  `Prints a list of all connected device's UDIDs. If --details is specified, it includes version, name and model of each device.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return printDeviceList(o)
		},
	}

	cmd.Flags().BoolVar(&o.Details, "details", false, "Include version, name and model of each device")

	return cmd
}

func printDeviceList(opts *Options) error {
	devices, err := ios.ListDevices()
	if err != nil {
		return fmt.Errorf("error listing devices: %w", err)
	} else if len(devices.DeviceList) == 0 {
		fmt.Println("no devices found")
		return nil
	}

	if opts.Details {
		if opts.JSONDisabled {
			return outputDetailedListWithNoJSON(devices.DeviceList)
		} else {
			return outputDetailedList(devices.DeviceList, opts.Options)
		}
	} else {
		if opts.JSONDisabled {
			fmt.Println(devices.String())
			return nil
		} else {
			return helpers.PrintJSON(devices.CreateMapForJSONConverter(), opts.Options)
		}
	}
}

func outputDetailedListWithNoJSON(devices []ios.DeviceEntry) error {
	for _, device := range devices {
		values, err := ios.GetValues(device)
		if err != nil {
			return fmt.Errorf("error getting values: %w", err)
		}

		fmt.Printf(
			"%s %s %s %s\n",
			device.Properties.SerialNumber, values.Value.ProductName, values.Value.ProductType,
			values.Value.ProductVersion,
		)
	}
	return nil
}

func outputDetailedList(devices []ios.DeviceEntry, opts *options.Options) error {
	result := make([]detailsEntry, 0, len(devices))
	for _, device := range devices {
		values, err := ios.GetValues(device)
		if err != nil {
			return fmt.Errorf("error getting values: %w", err)
		}

		result = append(
			result, detailsEntry{
				Udid:           device.Properties.SerialNumber,
				ProductName:    values.Value.ProductName,
				ProductType:    values.Value.ProductType,
				ProductVersion: values.Value.ProductVersion,
			},
		)
	}

	return helpers.PrintJSON(result, opts)
}
