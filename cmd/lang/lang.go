package lang

import (
	"fmt"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
)

type Options struct {
	*options.Options
	SetLocale string
	SetLang   string
}

// NewLangCmd creates a new lang command
func NewLangCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "lang",
		Short: "Manage device language and locale",
		Long: `Sets or gets the Device language. ios lang will print the current language and locale,
as well as a list of all supported langs and locales.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runLang(o)
		},
	}

	cmd.Flags().StringVar(&o.SetLocale, "set-locale", "", "Set device locale")
	cmd.Flags().StringVar(&o.SetLang, "set-lang", "", "Set device language")

	return cmd
}

func runLang(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	log.Debugf("lang --setlocale:%s --setlang:%s", opts.SetLocale, opts.SetLang)

	// This feature requires more complex implementation
	// For now, just return an informational message
	if opts.SetLocale != "" || opts.SetLang != "" {
		fmt.Printf("Language/locale setting is not yet implemented\n")
		fmt.Printf("Requested language: %s, locale: %s\n", opts.SetLang, opts.SetLocale)
		fmt.Printf("Device: %s\n", device.Properties.SerialNumber)
		return nil
	}

	// Get basic device info instead of language info
	result := map[string]any{
		"serial_number": device.Properties.SerialNumber,
		"message":       "Language/locale querying is not yet implemented",
	}

	return helpers.PrintJSON(result, opts.Options)
}
