package listen

import (
	"fmt"
	"log/slog"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/ios"
)

// DeviceConnectionEvent represents a device connection or disconnection event
type DeviceConnectionEvent struct {
	DeviceID  string
	Connected bool
}

// DeviceConnectionListener listens for device connections and disconnections
type DeviceConnectionListener struct {
	stop chan struct{}
}

// NewDeviceConnectionListener creates a new DeviceConnectionListener
func NewDeviceConnectionListener() (*DeviceConnectionListener, error) {
	return &DeviceConnectionListener{
		stop: make(chan struct{}),
	}, nil
}

// Stop stops the listener
func (l *DeviceConnectionListener) Stop() {
	close(l.stop)
}

// StartListening starts listening for device connections and disconnections
func (l *DeviceConnectionListener) StartListening(callback func(DeviceConnectionEvent)) {
	go func() {
		var (
			deviceConn ios.DeviceConnectionInterface
			err        error
		)
		defer func() {
			if deviceConn != nil {
				_ = deviceConn.Close()
			}
		}()

		for {
			select {
			case <-l.stop:
				return
			default:
				deviceConn, err = ios.NewDeviceConnection(ios.GetUsbmuxdSocket())
				if err != nil {
					slog.Error("could not connect, will retry in 3 seconds...",
						"socket", ios.GetUsbmuxdSocket(), "error", err)
					time.Sleep(time.Second * 3)
					continue
				}
				muxConnection := ios.NewUsbMuxConnection(deviceConn)

				attachedReceiver, err := muxConnection.Listen()
				if err != nil {
					slog.Error("Failed issuing Listen command, will retry in 3 seconds", "error", err)
					_ = deviceConn.Close()
					time.Sleep(time.Second * 3)
					continue
				}
				for {
					select {
					case <-l.stop:
						return
					default:
						msg, err := attachedReceiver()
						if err != nil {
							slog.Error("Stopped listening because of error", "error", err)
							break
						}

						event := DeviceConnectionEvent{
							DeviceID:  msg.Properties.SerialNumber,
							Connected: msg.DeviceAttached(),
						}
						callback(event)
					}
				}
			}
		}
	}()
}

// NewListenCmd creates a new listen command
func NewListenCmd() *cobra.Command {
	listenCmd := &cobra.Command{
		Use:   "listen",
		Short: "Listen for device connections and disconnections",
		Long:  `Keeps a persistent connection open and notifies about newly connected or disconnected devices.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return startListening()
		},
	}

	return listenCmd
}

func startListening() error {
	listener, err := NewDeviceConnectionListener()
	if err != nil {
		return fmt.Errorf("error creating device connection listener: %w", err)
	}

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-c
		slog.Info("Stopping listener")
		listener.Stop()
		os.Exit(0)
	}()

	slog.Info("Listening for device connections and disconnections. Press Ctrl+C to stop.")
	listener.StartListening(func(event DeviceConnectionEvent) {
		if event.Connected {
			slog.Info("Device connected", "udid", event.DeviceID)
		} else {
			slog.Info("Device disconnected", "udid", event.DeviceID)
		}
	})

	// Wait for signal
	<-c
	return nil
}
