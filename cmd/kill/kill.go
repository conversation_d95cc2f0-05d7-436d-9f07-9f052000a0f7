package kill

import (
	"fmt"

	log "github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
	"github.com/danielpaulus/go-ios/ios/installationproxy"
	"github.com/danielpaulus/go-ios/ios/instruments"
)

type Options struct {
	*options.Options

	BundleID string
	Pid      int
	Process  string
}

// NewKillCmd creates a new kill command
func NewKillCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}
	cmd := &cobra.Command{
		Use:   "kill",
		Short: "Kill a process",
		Long:  `Kill app with the specified bundleID, process id, or process name on the device.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			var bundleID string
			if len(args) > 0 {
				bundleID = args[0]
			}

			// Technically "Mach Kernel" is process 0, I suppose we provide no way to attempt to kill that.
			if bundleID == "" && o.Pid == 0 && o.Process == "" {
				return fmt.Errorf("please provide a bundleID, process ID, or process name")
			}

			if bundleID != "" {
				o.BundleID = bundleID
			}

			dev, err := helpers.GetDevice(o.Options)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return killProcess(dev, o)
		},
	}

	// Add flags
	cmd.Flags().StringVar(&o.BundleID, "bundle-id", "", "Bundle ID to kill")
	cmd.Flags().IntVar(&o.Pid, "pid", 0, "Process ID to kill")
	cmd.Flags().StringVar(&o.Process, "process", "", "Process name to kill")

	return cmd
}

func killProcess(device ios.DeviceEntry, opts *Options) error {
	var (
		response    []installationproxy.AppInfo
		processName string
	)

	pControl, err := instruments.NewProcessControl(device)
	if err != nil {
		return fmt.Errorf("error creating process control: %w", err)
	}

	// Look for the correct process exe name for this bundleID. By default, searches only user-installed apps.
	if opts.BundleID != "" {
		svc, err := installationproxy.New(device)
		if err != nil {
			return fmt.Errorf("error creating installation proxy: %w", err)
		}

		response, err = svc.BrowseAllApps()
		if err != nil {
			return fmt.Errorf("error browsing apps: %w", err)
		}

		for _, app := range response {
			if app.CFBundleIdentifier == opts.BundleID {
				processName = app.CFBundleExecutable
				break
			}
		}
		if processName == "" {
			return fmt.Errorf("%s not installed", opts.BundleID)
		}
	} else if opts.Process != "" {
		processName = opts.Process
	}

	service, err := instruments.NewDeviceInfoService(device)
	if err != nil {
		return fmt.Errorf("error creating device info service: %w", err)
	}
	defer service.Close()

	processList, err := service.ProcessList()
	if err != nil {
		return fmt.Errorf("error getting process list: %w", err)
	}

	// Find and kill the process
	for _, p := range processList {
		if (opts.Pid > 0 && p.Pid == uint64(opts.Pid)) || (processName != "" && p.Name == processName) {
			err = pControl.KillProcess(p.Pid)
			if err != nil {
				return fmt.Errorf("error killing process: %w", err)
			}
			if opts.BundleID != "" {
				log.Infof("%s killed, Pid: %d", opts.BundleID, p.Pid)
			} else {
				log.Infof("%s killed, Pid: %d", p.Name, p.Pid)
			}
			return nil
		}
	}

	// Process not found
	if opts.BundleID != "" {
		return fmt.Errorf("process of %s not found", opts.BundleID)
	} else if processName != "" {
		return fmt.Errorf("process named %s not found", processName)
	} else {
		return fmt.Errorf("process with pid %d not found", opts.Pid)
	}
}
