package devicename

import (
	"fmt"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios"
)

// NewDeviceNameCmd creates a new device-name command
func NewDeviceNameCmd(opts *options.Options) *cobra.Command {
	return &cobra.Command{
		Use:   "device-name",
		Short: "Print device name",
		Long:  `Prints the device name.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			dev, err := helpers.GetDevice(opts)
			if err != nil {
				return fmt.Errorf("error getting device: %w", err)
			}

			return printDeviceName(dev, opts)
		},
	}
}

func printDeviceName(device ios.DeviceEntry, opts *options.Options) error {
	lockdown, err := ios.ConnectLockdownWithSession(device)
	if err != nil {
		return fmt.Errorf("error connecting to lockdown: %w", err)
	}
	defer lockdown.Close()

	value, err := lockdown.GetValue("DeviceName")
	if err != nil {
		return fmt.Errorf("error getting device name: %w", err)
	}

	if opts.JSONDisabled {
		fmt.Printf("device name: %v\n", value)
		return nil
	}

	s, err := helpers.ConvertToJSONString(value, opts.PrettyJSON)
	if err != nil {
		return err
	}

	fmt.Println(s)
	return nil
}
