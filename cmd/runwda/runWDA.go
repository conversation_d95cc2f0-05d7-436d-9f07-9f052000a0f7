package runwda

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"github.com/spf13/cobra"

	"github.com/danielpaulus/go-ios/cmd/helpers"
	"github.com/danielpaulus/go-ios/cmd/options"
	"github.com/danielpaulus/go-ios/ios/testmanagerd"
)

type Options struct {
	*options.Options

	BundleID           string
	TestRunnerBundleId string
	XctestConfig       string
	LogOutput          string
	Args               []string
	Env                []string
}

// NewRunWdaCmd creates a new run-wda command
func NewRunWdaCmd(opts *options.Options) *cobra.Command {
	o := &Options{Options: opts}

	cmd := &cobra.Command{
		Use:   "run-wda",
		Short: "Run WebDriverAgent",
		Long: `Runs WebDriverAgents.
Specify runtime args and env vars like --env ENV_1=something --env ENV_2=else and --arg ARG1 --arg ARG2.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runRunWda(o)
		},
	}

	cmd.Flags().StringVar(&o.BundleID, "bundle-id", "", "Bundle ID of WebDriverAgent")
	cmd.Flags().StringVar(&o.TestRunnerBundleId, "test-runner-bundle-id", "", "Test runner bundle ID")
	cmd.Flags().StringVar(&o.XctestConfig, "xctest-config", "", "XCTest configuration")
	cmd.Flags().StringVar(&o.LogOutput, "log-output", "", "Log output file ('-' for stdout)")
	cmd.Flags().StringSliceVar(&o.Args, "arg", []string{}, "Runtime arguments")
	cmd.Flags().StringSliceVar(&o.Env, "env", []string{}, "Environment variables (KEY=VALUE)")

	return cmd
}

func runRunWda(opts *Options) error {
	device, err := helpers.GetDevice(opts.Options)
	if err != nil {
		return fmt.Errorf("error getting device: %w", err)
	}

	// Set defaults if not specified
	if opts.BundleID == "" && opts.TestRunnerBundleId == "" && opts.XctestConfig == "" {
		slog.Info("no bundle ids specified, falling back to defaults")
		opts.BundleID = "com.facebook.WebDriverAgentRunner.xctrunner"
		opts.TestRunnerBundleId = "com.facebook.WebDriverAgentRunner.xctrunner"
		opts.XctestConfig = "WebDriverAgentRunner.xctest"
	}

	if opts.BundleID == "" || opts.TestRunnerBundleId == "" || opts.XctestConfig == "" {
		return fmt.Errorf("please specify either NONE of bundleid, testbundleid and xctestconfig or ALL of them")
	}

	slog.Info("Running wda",
		"bundleid", opts.BundleID,
		"testbundleid", opts.TestRunnerBundleId,
		"xctestconfig", opts.XctestConfig)

	// Parse environment variables
	env := make(map[string]any)
	for _, envVar := range opts.Env {
		parts := strings.SplitN(envVar, "=", 2)
		if len(parts) == 2 {
			env[parts[0]] = parts[1]
		}
	}

	// Convert args to string slice (keep as is)
	args := opts.Args

	// Set up output writer
	var writer io.Writer = io.Discard
	if opts.LogOutput != "" {
		if opts.LogOutput == "-" {
			writer = os.Stdout
		} else {
			file, err := os.Create(opts.LogOutput)
			if err != nil {
				return fmt.Errorf("error creating log file: %w", err)
			}
			defer file.Close()
			writer = file
		}
	}

	config := testmanagerd.TestConfig{
		BundleId:           opts.BundleID,
		TestRunnerBundleId: opts.TestRunnerBundleId,
		XctestConfigName:   opts.XctestConfig,
		Env:                env,
		Args:               args,
		Device:             device,
		Listener:           testmanagerd.NewTestListener(writer, writer, os.TempDir()),
	}

	errorChannel := make(chan error)
	defer close(errorChannel)
	ctx, stopWda := context.WithCancel(context.Background())

	go func() {
		_, err := testmanagerd.RunTestWithConfig(ctx, config)
		if err != nil {
			errorChannel <- err
		}
		stopWda()
	}()

	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGINT, syscall.SIGTERM)

	select {
	case err := <-errorChannel:
		slog.Error("Failed running WDA", "error", err)
		stopWda()
		return err
	case <-ctx.Done():
		slog.Error("WDA process ended unexpectedly")
		return fmt.Errorf("WDA process ended unexpectedly")
	case signal := <-c:
		slog.Info("os signal received, closing...", "signal", signal)
		stopWda()
	}

	slog.Info("Done Closing")
	return nil
}
