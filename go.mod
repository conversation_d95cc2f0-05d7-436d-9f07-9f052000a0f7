module github.com/danielpaulus/go-ios

go 1.23

require (
	github.com/Masterminds/semver v1.5.0
	github.com/google/gopacket v1.1.19
	github.com/google/uuid v1.1.2
	github.com/grandcat/zeroconf v1.0.0
	github.com/lunixbochs/struc v0.0.0-20200707160740-784aaebc1d40
	github.com/pierrec/lz4 v2.6.1+incompatible
	github.com/quic-go/quic-go v0.40.1-0.20231203135336-87ef8ec48d55
	github.com/sirupsen/logrus v1.9.3
	github.com/songgao/water v0.0.0-20200317203138-2b4b6d7c09d8
	github.com/spf13/cobra v1.9.1
	github.com/stretchr/testify v1.7.0
	github.com/tadglines/go-pkgs v0.0.0-20210623144937-b983b20f54f9
	go.mozilla.org/pkcs7 v0.0.0-20210826202110-33d05740a352
	golang.org/x/crypto v0.24.0
	golang.org/x/exp v0.0.0-20230725093048-515e97ebf090
	golang.org/x/net v0.26.0
	golang.org/x/sys v0.21.0
	golang.zx2c4.com/wintun v0.0.0-20230126152724-0fa3db229ce2
	gvisor.dev/gvisor v0.0.0-20240405191320-0878b34101b5
	howett.net/plist v0.0.0-20200419221736-3b63eb3a43b5
	software.sslmate.com/src/go-pkcs12 v0.2.0
)

require (
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/spf13/pflag v1.0.6 // indirect
)

require (
	github.com/cenkalti/backoff v2.2.1+incompatible // indirect
	github.com/davecgh/go-spew v1.1.1 // indirect
	github.com/elazarl/goproxy v0.0.0-20240726154733-8b0c20506380
	github.com/frankban/quicktest v1.14.6 // indirect
	github.com/go-task/slim-sprig v0.0.0-20230315185526-52ccab3ef572 // indirect
	github.com/google/btree v1.1.2 // indirect
	github.com/google/pprof v0.0.0-20210407192527-94a9f03dee38 // indirect
	github.com/miekg/dns v1.1.57 // indirect
	github.com/onsi/ginkgo/v2 v2.9.5 // indirect
	github.com/pmezard/go-difflib v1.0.0 // indirect
	github.com/quic-go/qtls-go1-20 v0.4.1 // indirect
	github.com/stretchr/objx v0.1.0 // indirect
	go.uber.org/mock v0.3.0 // indirect
	golang.org/x/mod v0.17.0 // indirect
	golang.org/x/sync v0.7.0 // indirect
	golang.org/x/text v0.16.0 // indirect
	golang.org/x/time v0.5.0 // indirect
	golang.org/x/tools v0.21.1-0.20240508182429-e35e4ccd0d2d // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
)
