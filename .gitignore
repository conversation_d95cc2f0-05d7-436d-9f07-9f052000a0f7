__debug_bin*
selfIdentity.plist
cmd/cdc-ncm/cdc-ncm
node_modules
*.mobileprovision
*.p12
*.key
*.der
*.pem
*.csr
testdata/wda-signed.ipa
devimages
# Binaries for programs and plugins
*.log
*.exe
*.exe~
*.dll
*.so
*.dylib
go-ios
usbmuxd
main
go-ncm
*.png
!logo.png
# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
.idea
.vscode
.DS_Store
.circleci/
__debug_bin
dump-*
