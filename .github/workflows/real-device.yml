on:
  workflow_dispatch:
    inputs:
      ngrok:
        description: 'ngrok host:port'
        required: true
        default: ''
      udid:
        description: 'device udid'
        required: true
        default: ''
# run this locally to get a device exposed on ngrok
# ngrok tcp 9999
# socat TCP-LISTEN:9999,reuse<PERSON>dr,fork UNIX-CONNECT:/var/run/usbmuxd

name: CI Real Device Test
jobs:
  sign_app_on_mac:
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v3
      - name: create profile
        run: |
          echo "$P12_FILE" | base64 --decode > testdata/test1.p12
          echo "$PROFILE" | base64 --decode > testdata/test1.mobileprovision
        shell: bash
        env:
          P12_FILE: ${{ secrets.P12 }}
          PROFILE: ${{ secrets.PROFILE }}

      - name: Run App Signer
        run: |
          testdata/app-signer-mac --udid=$UDID --p12password=$P12_PW --profilespath=testdata --ipa=testdata/wda.ipa --output=testdata/wda-signed.ipa
        env:
          P12_PW: ${{secrets.P12PASSWORD}}
          UDID: ${{ github.event.inputs.udid }}

      - name: Delete profiles
        if: always()
        run: |
          rm testdata/test1*

      - name: upload the macos build
        uses: actions/upload-artifact@v4
        with:
          name: signed-wda
          path: testdata/wda-signed.ipa
          retention-days: 1
          overwrite: true

  test_on_windows:
    runs-on: windows-latest
    needs: sign_app_on_mac
    env:
      UDID: ${{ github.event.inputs.udid }}
    steps:
      - uses: actions/checkout@v3

      - name: Install Go
        uses: actions/setup-go@v3
        with:
          go-version-file: go.mod
          cache: true

      - name: compile
        run: go build
      - name: Download mac signed wda from previous job
        uses: actions/download-artifact@v4
        with:
          name: signed-wda

      - name:
          extract ipa #github auto zips artifacts after a workflow is complete so we need to unzip the ipa
          #as it is now double zipped
        run: Expand-Archive -Force wda-signed.ipa testdata/signed-wda.ipa

      - name: forward ngrok
        run: netsh interface portproxy add v4tov4 listenaddress=127.0.0.1 listenport=27015 connectaddress=$($env:NGROK_URL.Split(":")[0]) connectport=$($env:NGROK_URL.Split(":")[1])
        env:
          NGROK_URL: ${{ github.event.inputs.ngrok }}

      - name: test list
        run: go run main.go list

      - name: install relative
        run: go run main.go install --path testdata\signed-wda.ipa --udid=$($env:UDID)

      - name: install absolute
        run: go run main.go install --path $PWD\testdata\signed-wda.ipa --udid=$($env:UDID)

  test_on_linux:
    runs-on: ubuntu-latest
    env:
      UDID: ${{ github.event.inputs.udid }}
    needs:
      - sign_app_on_mac
      - test_on_windows
    steps:
      - uses: actions/checkout@v3

      - name: Install Go
        uses: actions/setup-go@v3
        with:
          go-version-file: go.mod
          cache: true

      - name: Download mac release from previous job
        uses: actions/download-artifact@v4
        with:
          name: signed-wda

      - name: extract
        run: |
          mv wda-signed.ipa testdata/signed-wda.ipa

      - name: compile
        run: go build

      - name: install socat
        run: sudo apt install socat

      - name: ngrok
        run: sudo socat UNIX-LISTEN:/var/run/usbmuxd,fork,mode=777 TCP-CONNECT:$NGROK_URL&
        env:
          NGROK_URL: ${{ github.event.inputs.ngrok }}

      - name: test list
        run: go run main.go list

      - name: install relative
        run: go run main.go install --path testdata/signed-wda.ipa --udid=$UDID
