name: Deploy

on: [workflow_dispatch]

jobs:
  deploy:
    name: Deploy my app
    runs-on: self-hosted

    steps:
      - uses: actions/checkout@v3

      - name: Extract ngrok url
        run: |
          echo "NGROK_URL=$(curl -s localhost:4040/api/tunnels | jq -r ".tunnels[0].public_url")" >> $GITHUB_ENV
          curl -s localhost:4040/api/tunnels | jq ".tunnels[0].public_url"
          cat "$GITHUB_ENV"

      - uses: chrnorm/deployment-action@v2
        name: Create GitHub deployment
        id: deployment
        with:
          token: '${{ github.token }}'
          environment-url: ${{ env.NGROK_URL }}
          environment: staging

      - name: Install Go
        uses: actions/setup-go@v3
        with:
          go-version-file: go.mod
          cache: true

      - name: kill old server
        run: systemctl --user stop goios
      - name: compile
        run: |
          cd restapi
          go install github.com/swaggo/swag/cmd/swag@latest
          swag init
          go build

      - name: Deploy my app
        run: |
          cd restapi
          cp ./restapi /home/<USER>/
          rm -rf /home/<USER>/docs
          mkdir /home/<USER>/docs
          cp -R ./docs /home/<USER>/docs
          systemctl --user start goios

      - name: Update deployment status (success)
        if: success()
        uses: chrnorm/deployment-status@v2
        with:
          token: '${{ github.token }}'
          environment-url: ${{ steps.deployment.outputs.environment_url }}
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
          state: 'success'

      - name: Update deployment status (failure)
        if: failure()
        uses: chrnorm/deployment-status@v2
        with:
          token: '${{ github.token }}'
          environment-url: ${{ steps.deployment.outputs.environment_url }}
          deployment-id: ${{ steps.deployment.outputs.deployment_id }}
          state: 'failure'
