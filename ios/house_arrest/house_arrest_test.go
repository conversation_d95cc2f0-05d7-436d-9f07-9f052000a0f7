package house_arrest_test

/*
import (
	"testing"

	log "github.com/sirupsen/logrus"

	"github.com/danielpaulus/go-ios/usbmux"
	"github.com/danielpaulus/go-ios/usbmux/house_arrest"
)


func TestIT(t *testing.T) {
	device := ios.ListDevices().DeviceList[0]
	conn, err := house_arrest.New(device.DeviceID, device.Properties.SerialNumber, "d.blaUITests.xctrunner")
	list, err := conn.ListFiles("tmp")
	log.Info(list)
	filePath := "tmp/test1003.txt"
	err = conn.SendFile([]byte("tesdvdsfdfgdfdfgst"), filePath)
	if err != nil {
		log.Fatal(err)
	}

	defer conn.Close()
	log.Fatal(err)
}
*/
