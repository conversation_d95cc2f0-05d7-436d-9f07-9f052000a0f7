package ios

import (
	"bytes"
	"fmt"

	"howett.net/plist"
)

// ListenType contains infos for creating a LISTEN message for USBMUX
type ListenType struct {
	MessageType         string
	ProgName            string
	ClientVersionString string
	ConnType            int
	kLibUSBMuxVersion   int
}

// AttachedMessage contains some info about when iOS devices are connected or disconnected from the host
type AttachedMessage struct {
	MessageType string
	DeviceID    int
	Properties  DeviceProperties
}

// DeviceEntry converts the attachedMessage to a DeviceEntry
func (msg AttachedMessage) DeviceEntry() DeviceEntry {
	return DeviceEntry{DeviceID: msg.DeviceID, MessageType: "Attached", Properties: msg.Properties}
}

// DeviceAttached checks if the attached message is about a newly added device
func (msg AttachedMessage) DeviceAttached() bool {
	return "Attached" == msg.MessageType
}

// DeviceDetached checks if the attachedMessage is about a disconnected device
func (msg AttachedMessage) DeviceDetached() bool {
	return "Detached" == msg.MessageType
}

func attachedFromBytes(plistBytes []byte) (AttachedMessage, error) {
	decoder := plist.NewDecoder(bytes.NewReader(plistBytes))
	var obj AttachedMessage
	err := decoder.Decode(&obj)
	if err != nil {
		return obj, err
	}
	return obj, nil
}

// NewListen creates a new Listen Message for USBMUX
func NewListen() ListenType {
	data := ListenType{
		MessageType:         "Listen",
		ProgName:            "go-usbmux",
		ClientVersionString: "usbmuxd-471.8.1",
		// Seems like Conntype is not really needed
		ConnType:          1,
		kLibUSBMuxVersion: 3,
	}
	return data
}

// Listen will send a listen command to usbmuxd, which will cause this connection to stay open indefinitely and receive
// messages whenever devices are connected or disconnected
func (muxConn *UsbMuxConnection) Listen() (func() (AttachedMessage, error), error) {
	err := muxConn.Send(NewListen())
	if err != nil {
		return nil, err
	}

	response, err := muxConn.ReadMuxMessage()
	if err != nil {
		return nil, err
	}

	if !MuxResponsefromBytes(response.Payload).IsSuccessFull() {
		return nil, fmt.Errorf("listen command to usbmuxd failed: %x", response.Payload)
	}

	return muxConn.getAttachedMessage, nil
}

func (muxConn *UsbMuxConnection) getAttachedMessage() (AttachedMessage, error) {
	mux, err := muxConn.ReadMuxMessage()
	if err != nil {
		return AttachedMessage{}, err
	}

	return attachedFromBytes(mux.Payload)
}

func Listen() (func() (AttachedMessage, error), func() error, error) {
	deviceConn, err := NewDeviceConnection(GetUsbmuxdSocket())
	if err != nil {
		return nil, nil, fmt.Errorf("could not connect to usbmuxd: %w", err)
	}

	muxConnection := NewUsbMuxConnection(deviceConn)
	attachedReceiver, err := muxConnection.Listen()
	return attachedReceiver, muxConnection.Close, err
}
