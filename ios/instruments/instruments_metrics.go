package instruments

import (
	log "github.com/sirupsen/logrus"

	"github.com/danielpaulus/go-ios/ios"
	dtx "github.com/danielpaulus/go-ios/ios/dtx_codec"
)

type metricsDispatcher struct {
	messageChannel chan dtx.Message
	closeChannel   chan struct{}
}

func (dispatcher metricsDispatcher) Dispatch(msg dtx.Message) {
	log.Infof("%+v", msg)
}

func GetMetrics(device ios.DeviceEntry) (func() (map[string]any, error), func() error, error) {
	conn, err := connectInstruments(device)
	if err != nil {
		return nil, nil, err
	}
	dispatcher := metricsDispatcher{messageChannel: make(chan dtx.Message), closeChannel: make(chan struct{})}
	conn.AddDefaultChannelReceiver(dispatcher)
	channel := conn.RequestChannelIdentifier(mobileNotificationsChannel, channelDispatcher{})
	resp, err := channel.MethodCall("setApplicationStateNotificationsEnabled:", true)
	if err != nil {
		log.Errorf("resp:%+v, %+v", resp, resp.Payload[0])
		return nil, nil, err
	}
	log.Debugf("appstatenotifications enabled successfully: %+v", resp)
	resp, err = channel.MethodCall("setMemoryNotificationsEnabled:", true)
	if err != nil {
		log.Errorf("resp:%+v, %+v", resp, resp.Payload[0])
		return nil, nil, err
	}
	log.Debugf("memory notifications enabled: %+v", resp)

	return nil, nil, nil
}
