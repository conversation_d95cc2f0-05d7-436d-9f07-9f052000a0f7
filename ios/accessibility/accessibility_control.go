package accessibility

import (
	"fmt"

	log "github.com/sirupsen/logrus"

	dtx "github.com/danielpaulus/go-ios/ios/dtx_codec"
	"github.com/danielpaulus/go-ios/ios/nskeyedarchiver"
)

// ControlInterface provides a simple interface to controlling the AX service on the device
// It only needs the global dtx channel as all AX methods are invoked on it.
type ControlInterface struct {
	channel *dtx.Channel
}

func (a ControlInterface) readhostAppStateChanged() {
	for {
		msg := a.channel.ReceiveMethodCall("hostAppStateChanged:")
		stateChange, err := nskeyedarchiver.Unarchive(msg.Auxiliary.GetArguments()[0].([]byte))
		if err != nil {
			panic(err)
		}
		value := stateChange[0]
		log.Infof("hostAppStateChanged:%s", value)
	}
}

func (a ControlInterface) readhostInspectorNotificationReceived() {
	for {
		msg := a.channel.ReceiveMethodCall("hostInspectorNotificationReceived:")
		notification, err := nskeyedarchiver.Unarchive(msg.Auxiliary.GetArguments()[0].([]byte))
		if err != nil {
			panic(err)
		}
		value := notification[0].(map[string]any)["Value"]
		log.Infof("hostInspectorNotificationReceived:%s", value)
	}
}

// init wires up event receivers and gets Info from the device
func (a ControlInterface) init() error {
	a.channel.RegisterMethodForRemote("hostInspectorCurrentElementChanged:")
	a.channel.RegisterMethodForRemote("hostInspectorMonitoredEventTypeChanged:")
	a.channel.RegisterMethodForRemote("hostAppStateChanged:")
	a.channel.RegisterMethodForRemote("hostInspectorNotificationReceived:")
	go a.readhostAppStateChanged()
	go a.readhostInspectorNotificationReceived()

	err := a.notifyPublishedCapabilities()
	if err != nil {
		return err
	}
	// a list of methods we are allowed to call on the device
	deviceCapabilities, err := a.deviceCapabilities()
	if err != nil {
		return err
	}

	log.Info("Device Capabilities:", deviceCapabilities)
	apiVersion, err := a.deviceAPIVersion()
	if err != nil {
		return err
	}
	log.Info("Api version:", apiVersion)

	auditCaseIds, err := a.deviceAllAuditCaseIDs()
	if err != nil {
		return err
	}
	log.Info("AuditCaseIDs", auditCaseIds)

	deviceInspectorSupportedEventTypes, err := a.deviceInspectorSupportedEventTypes()
	if err != nil {
		return err
	}
	log.Info("deviceInspectorSupportedEventTypes:", deviceInspectorSupportedEventTypes)

	canNav, err := a.deviceInspectorCanNavWhileMonitoringEvents()
	if err != nil {
		return err
	}
	log.Info("deviceInspectorCanNavWhileMonitoringEvents:", canNav)

	err = a.deviceSetAppMonitoringEnabled(true)
	if err != nil {
		return err
	}

	for _, v := range auditCaseIds {
		name, err := a.deviceHumanReadableDescriptionForAuditCaseID(v)
		if err != nil {
			return err
		}
		log.Infof("%s -- %s", v, name)
	}
	return nil
}

// EnableSelectionMode enables the UI element selection mode on the device,
// it is the same as clicking the little crosshair in AX Inspector
func (a ControlInterface) EnableSelectionMode() error {
	if err := a.deviceInspectorSetMonitoredEventType(2); err != nil {
		return fmt.Errorf("failed to set monitored event type: %w", err)
	}
	if err := a.deviceInspectorShowVisuals(true); err != nil {
		return fmt.Errorf("failed to show visuals: %w", err)
	}
	a.awaitHostInspectorMonitoredEventTypeChanged()
	return nil
}

// SwitchToDevice is the same as switching to the Device in AX inspector.
// After running this, notifications and events should be received.
func (a ControlInterface) SwitchToDevice() error {
	if err := a.TurnOff(); err != nil {
		return fmt.Errorf("failed to turn off: %w", err)
	}
	resp, err := a.deviceAccessibilitySettings()
	if err != nil {
		return fmt.Errorf("failed to get accessibility settings: %w", err)
	}
	log.Info("AX Settings received:", resp)
	if err := a.deviceInspectorShowIgnoredElements(false); err != nil {
		return fmt.Errorf("failed to show ignored elements: %w", err)
	}
	if err := a.deviceSetAuditTargetPid(0); err != nil {
		return fmt.Errorf("failed to set audit target pid: %w", err)
	}
	if err := a.deviceInspectorFocusOnElement(); err != nil {
		return fmt.Errorf("failed to focus on element: %w", err)
	}
	a.awaitHostInspectorCurrentElementChanged()
	if err := a.deviceInspectorPreviewOnElement(); err != nil {
		return fmt.Errorf("failed to preview on element: %w", err)
	}
	if err := a.deviceHighlightIssue(); err != nil {
		return fmt.Errorf("failed to highlight issue: %w", err)
	}
	return nil
}

// TurnOff disable AX
func (a ControlInterface) TurnOff() error {
	if err := a.deviceInspectorSetMonitoredEventType(0); err != nil {
		return fmt.Errorf("failed to set monitored event type: %w", err)
	}
	a.awaitHostInspectorMonitoredEventTypeChanged()
	if err := a.deviceInspectorFocusOnElement(); err != nil {
		return fmt.Errorf("failed to focus on element: %w", err)
	}
	a.awaitHostInspectorCurrentElementChanged()
	if err := a.deviceInspectorPreviewOnElement(); err != nil {
		return fmt.Errorf("failed to preview on element: %w", err)
	}
	if err := a.deviceHighlightIssue(); err != nil {
		return fmt.Errorf("failed to highlight issue: %w", err)
	}
	if err := a.deviceInspectorShowVisuals(false); err != nil {
		return fmt.Errorf("failed to show visuals: %w", err)
	}
	return nil
}

// GetElement moves the green selection rectangle one element further
func (a ControlInterface) GetElement() (map[string]any, error) {
	log.Info("changing")
	if err := a.deviceInspectorMoveWithOptions(); err != nil {
		return nil, fmt.Errorf("failed to move with options: %w", err)
	}
	// a.deviceInspectorMoveWithOptions()

	resp := a.awaitHostInspectorCurrentElementChanged()
	log.Info("item changed", resp)
	return resp, nil
}

func (a ControlInterface) UpdateAccessibilitySetting(name string, val any) error {
	log.Infof("starting updating accessibility setting: %s", name)

	resp, err := a.updateAccessibilitySetting(name, val)
	if err != nil {
		return fmt.Errorf("failed to update accessibility setting: %w", err)
	}

	log.Infof("finished updating accessibility setting: %s", resp)
	return nil
}

func (a ControlInterface) ResetToDefaultAccessibilitySettings() error {
	err := a.channel.MethodCallAsync("deviceResetToDefaultAccessibilitySettings")
	if err != nil {
		return err
	}
	return nil
}

func (a ControlInterface) awaitHostInspectorCurrentElementChanged() map[string]any {
	msg := a.channel.ReceiveMethodCall("hostInspectorCurrentElementChanged:")
	log.Info("received hostInspectorCurrentElementChanged")
	result, err := nskeyedarchiver.Unarchive(msg.Auxiliary.GetArguments()[0].([]byte))
	if err != nil {
		panic(fmt.Sprintf("Failed unarchiving: %s this is a bug and should not happen", err))
	}
	return result[0].(map[string]any)
}

func (a ControlInterface) awaitHostInspectorMonitoredEventTypeChanged() {
	msg := a.channel.ReceiveMethodCall("hostInspectorMonitoredEventTypeChanged:")
	n, _ := nskeyedarchiver.Unarchive(msg.Auxiliary.GetArguments()[0].([]byte))
	log.Infof("hostInspectorMonitoredEventTypeChanged: was set to %d by the device", n[0])
}

func (a ControlInterface) deviceInspectorMoveWithOptions() error {
	method := "deviceInspectorMoveWithOptions:"
	options := nskeyedarchiver.NewNSMutableDictionary(
		map[string]any{
			"ObjectType": "passthrough",
			"Value": nskeyedarchiver.NewNSMutableDictionary(
				map[string]any{
					"allowNonAX": nskeyedarchiver.NewNSMutableDictionary(
						map[string]any{
							"ObjectType": "passthrough", "Value": false,
						},
					),
					"direction": nskeyedarchiver.NewNSMutableDictionary(
						map[string]any{
							"ObjectType": "passthrough", "Value": int32(4),
						},
					),
					"includeContainers": nskeyedarchiver.NewNSMutableDictionary(
						map[string]any{
							"ObjectType": "passthrough", "Value": true,
						},
					),
				},
			),
		},
	)
	// str, _ := nskeyedarchiver.ArchiveXML(options)
	// println(str)
	return a.channel.MethodCallAsync(method, options)
}

func (a ControlInterface) notifyPublishedCapabilities() error {
	capabs := map[string]any{
		"com.apple.private.DTXBlockCompression": uint64(2),
		"com.apple.private.DTXConnection":       uint64(1),
	}
	return a.channel.MethodCallAsync("_notifyOfPublishedCapabilities:", capabs)
}

func (a ControlInterface) deviceCapabilities() ([]string, error) {
	response, err := a.channel.MethodCall("deviceCapabilities")
	if err != nil {
		return nil, err
	}
	return convertToStringList(response.Payload)
}

func (a ControlInterface) deviceAllAuditCaseIDs() ([]string, error) {
	response, err := a.channel.MethodCall("deviceAllAuditCaseIDs")
	if err != nil {
		return nil, err
	}
	return convertToStringList(response.Payload)
}

func (a ControlInterface) deviceAccessibilitySettings() (map[string]any, error) {
	response, err := a.channel.MethodCall("deviceAccessibilitySettings")
	if err != nil {
		return nil, err
	}
	return response.Payload[0].(map[string]any), nil
}

func (a ControlInterface) deviceInspectorSupportedEventTypes() (uint64, error) {
	response, err := a.channel.MethodCall("deviceInspectorSupportedEventTypes")
	if err != nil {
		return 0, err
	}
	return response.Payload[0].(uint64), nil
}

func (a ControlInterface) deviceAPIVersion() (uint64, error) {
	response, err := a.channel.MethodCall("deviceApiVersion")
	if err != nil {
		return 0, err
	}
	return response.Payload[0].(uint64), nil
}

func (a ControlInterface) deviceInspectorCanNavWhileMonitoringEvents() (bool, error) {
	response, err := a.channel.MethodCall("deviceInspectorCanNavWhileMonitoringEvents")
	if err != nil {
		return false, err
	}
	return response.Payload[0].(bool), nil
}

func (a ControlInterface) deviceSetAppMonitoringEnabled(val bool) error {
	err := a.channel.MethodCallAsync("deviceSetAppMonitoringEnabled:", val)
	if err != nil {
		return err
	}
	return nil
}

func (a ControlInterface) updateAccessibilitySetting(settingName string, val any) (string, error) {
	setting := nskeyedarchiver.NewNSMutableDictionary(
		map[string]any{
			"ObjectType": "AXAuditDeviceSetting_v1",
			"Value": nskeyedarchiver.NewNSMutableDictionary(
				map[string]any{
					"ObjectType": "passthrough",
					"Value": nskeyedarchiver.NewNSMutableDictionary(
						map[string]any{
							"CurrentValueNumber_v1": nskeyedarchiver.NewNSMutableDictionary(
								map[string]any{
									"ObjectType": "passthrough",
									"Value":      true,
								},
							),
							"EnabledValue_v1": nskeyedarchiver.NewNSMutableDictionary(
								map[string]any{
									"ObjectType": "passthrough",
									"Value":      true,
								},
							),
							"IdentiifierValue_v1": nskeyedarchiver.NewNSMutableDictionary(
								map[string]any{
									"ObjectType": "passthrough",
									"Value":      settingName,
								},
							),
							"SettingTypeValue_v1": nskeyedarchiver.NewNSMutableDictionary(
								map[string]any{
									"ObjectType": "passthrough",
									"Value":      0,
								},
							),
							"SliderTickMarksValue_v1": nskeyedarchiver.NewNSMutableDictionary(
								map[string]any{
									"ObjectType": "passthrough",
									"Value":      0,
								},
							),
						},
					),
				},
			),
		},
	)

	value := nskeyedarchiver.NewNSMutableDictionary(
		map[string]any{
			"ObjectType": "passthrough",
			"Value":      val,
		},
	)

	response, err := a.channel.MethodCall("deviceUpdateAccessibilitySetting:withValue:", setting, value)
	if err != nil {
		return "", err
	}
	return response.PayloadHeader.String(), nil
}

func (a ControlInterface) deviceHumanReadableDescriptionForAuditCaseID(auditCaseID string) (string, error) {
	response, err := a.channel.MethodCall("deviceHumanReadableDescriptionForAuditCaseID:", auditCaseID)
	if err != nil {
		return "", err
	}
	return response.Payload[0].(string), nil
}

func (a ControlInterface) deviceInspectorShowIgnoredElements(val bool) error {
	return a.channel.MethodCallAsync("deviceInspectorShowIgnoredElements:", val)
}

func (a ControlInterface) deviceSetAuditTargetPid(pid uint64) error {
	return a.channel.MethodCallAsync("deviceSetAuditTargetPid:", pid)
}

func (a ControlInterface) deviceInspectorFocusOnElement() error {
	return a.channel.MethodCallAsync("deviceInspectorFocusOnElement:", nskeyedarchiver.NewNSNull())
}

func (a ControlInterface) deviceInspectorPreviewOnElement() error {
	return a.channel.MethodCallAsync("deviceInspectorPreviewOnElement:", nskeyedarchiver.NewNSNull())
}

func (a ControlInterface) deviceHighlightIssue() error {
	return a.channel.MethodCallAsync("deviceHighlightIssue:", map[string]any{})
}

func (a ControlInterface) deviceInspectorSetMonitoredEventType(eventtype uint64) error {
	return a.channel.MethodCallAsync("deviceInspectorSetMonitoredEventType:", eventtype)
}

func (a ControlInterface) deviceInspectorShowVisuals(val bool) error {
	return a.channel.MethodCallAsync("deviceInspectorShowVisuals:", val)
}
