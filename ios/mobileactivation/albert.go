package mobileactivation

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"time"
)

const activationUserAgent = "iOS Device Activator (MobileActivation-592.103.2)"

const (
	activationServerURL = "https://albert.apple.com/deviceservices/deviceActivation"
	drmHandshakeURL     = "https://albert.apple.com/deviceservices/drmHandshake"
)

var netClient = &http.Client{
	Timeout:   time.Second * 5,
	Transport: http.DefaultTransport,
}

// sendHandshakeRequest sends a handshake request to the DRM server
// The caller is responsible for closing the response body
func sendHandshakeRequest(body io.Reader) (http.Header, io.ReadCloser, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	requestURL := drmHandshakeURL
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, requestURL, body)
	if err != nil {
		return nil, nil, err
	}
	req.Header.Set("Content-Type", "application/x-apple-plist")
	req.Header.Set("Accept", "application/xml")
	req.Header.Set("User-Agent", activationUserAgent)

	// Create a custom client for this request to avoid linter warnings
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Use a nolint directive to suppress the bodyclose warning
	// The response body is intentionally not closed here because it's returned to the caller
	// who is responsible for closing it
	response, err := client.Do(req) //nolint:bodyclose
	if err != nil {
		return nil, nil, err
	}

	// Handle error status codes but don't close the body
	if response.StatusCode > 299 {
		// We intentionally don't close the body here because we're returning it
		return response.Header, response.Body, fmt.Errorf("error activating %d, %v", response.StatusCode, response)
	}

	return response.Header, response.Body, nil
}

// sendActivationRequest sends an activation request to the activation server
// The caller is responsible for closing the response body
func sendActivationRequest(body io.Reader) (http.Header, io.ReadCloser, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	requestURL := activationServerURL
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, requestURL, body)
	if err != nil {
		return nil, nil, err
	}
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "*/*")
	req.Header.Set("User-Agent", activationUserAgent)

	// Create a custom client for this request to avoid linter warnings
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// Use a nolint directive to suppress the bodyclose warning
	// The response body is intentionally not closed here because it's returned to the caller
	// who is responsible for closing it
	response, err := client.Do(req) //nolint:bodyclose
	if err != nil {
		return nil, nil, err
	}

	// Handle error status codes but don't close the body
	if response.StatusCode > 299 {
		// We intentionally don't close the body here because we're returning it
		return response.Header, response.Body, fmt.Errorf("error activating %d, %v", response.StatusCode, response)
	}

	return response.Header, response.Body, nil
}
