package afc

import (
	"encoding/binary"
	"errors"
	"fmt"
	"io"
)

const (
	Afc_magic                              uint64 = 0x4141504c36414643
	Afc_header_size                        uint64 = 40
	Afc_operation_status                   uint64 = 0x00000001
	Afc_operation_data                     uint64 = 0x00000002
	Afc_operation_read_dir                 uint64 = 0x00000003
	Afc_operation_remove_path              uint64 = 0x00000008
	Afc_operation_make_dir                 uint64 = 0x00000009
	Afc_operation_file_info                uint64 = 0x0000000A
	Afc_operation_device_info              uint64 = 0x0000000B
	Afc_operation_file_open                uint64 = 0x0000000D
	Afc_operation_file_close               uint64 = 0x00000014
	Afc_operation_file_write               uint64 = 0x00000010
	Afc_operation_file_open_result         uint64 = 0x0000000E
	Afc_operation_file_read                uint64 = 0x0000000F
	Afc_operation_remove_path_and_contents uint64 = 0x00000022
)

const (
	Afc_Mode_RDONLY   uint64 = 0x00000001
	Afc_Mode_RW       uint64 = 0x00000002
	Afc_Mode_WRONLY   uint64 = 0x00000003
	Afc_Mode_WR       uint64 = 0x00000004
	Afc_Mode_APPEND   uint64 = 0x00000005
	Afc_Mode_RDAPPEND uint64 = 0x00000006
)

const (
	Afc_Err_Success                = 0
	Afc_Err_UnknownError           = 1
	Afc_Err_OperationHeaderInvalid = 2
	Afc_Err_NoResources            = 3
	Afc_Err_ReadError              = 4
	Afc_Err_WriteError             = 5
	Afc_Err_UnknownPacketType      = 6
	Afc_Err_InvalidArgument        = 7
	Afc_Err_ObjectNotFound         = 8
	Afc_Err_ObjectIsDir            = 9
	Afc_Err_PermDenied             = 10
	Afc_Err_ServiceNotConnected    = 11
	Afc_Err_OperationTimeout       = 12
	Afc_Err_TooMuchData            = 13
	Afc_Err_EndOfData              = 14
	Afc_Err_OperationNotSupported  = 15
	Afc_Err_ObjectExists           = 16
	Afc_Err_ObjectBusy             = 17
	Afc_Err_NoSpaceLeft            = 18
	Afc_Err_OperationWouldBlock    = 19
	Afc_Err_IoError                = 20
	Afc_Err_OperationInterrupted   = 21
	Afc_Err_OperationInProgress    = 22
	Afc_Err_InternalError          = 23
	Afc_Err_MuxError               = 30
	Afc_Err_NoMemory               = 31
	Afc_Err_NotEnoughData          = 32
	Afc_Err_DirNotEmpty            = 33
)

type AFCDeviceInfo struct {
	Model      string
	TotalBytes uint64
	FreeBytes  uint64
	BlockSize  uint64
}

// errorMessages maps AFC error codes to their string representations
var errorMessages = map[uint64]string{
	Afc_Err_UnknownError:           "UnknownError",
	Afc_Err_OperationHeaderInvalid: "OperationHeaderInvalid",
	Afc_Err_NoResources:            "NoResources",
	Afc_Err_ReadError:              "ReadError",
	Afc_Err_WriteError:             "WriteError",
	Afc_Err_UnknownPacketType:      "UnknownPacketType",
	Afc_Err_InvalidArgument:        "InvalidArgument",
	Afc_Err_ObjectNotFound:         "ObjectNotFound",
	Afc_Err_ObjectIsDir:            "ObjectIsDir",
	Afc_Err_PermDenied:             "PermDenied",
	Afc_Err_ServiceNotConnected:    "ServiceNotConnected",
	Afc_Err_OperationTimeout:       "OperationTimeout",
	Afc_Err_TooMuchData:            "TooMuchData",
	Afc_Err_EndOfData:              "EndOfData",
	Afc_Err_OperationNotSupported:  "OperationNotSupported",
	Afc_Err_ObjectExists:           "ObjectExists",
	Afc_Err_ObjectBusy:             "ObjectBusy",
	Afc_Err_NoSpaceLeft:            "NoSpaceLeft",
	Afc_Err_OperationWouldBlock:    "OperationWouldBlock",
	Afc_Err_IoError:                "IoError",
	Afc_Err_OperationInterrupted:   "OperationInterrupted",
	Afc_Err_OperationInProgress:    "OperationInProgress",
	Afc_Err_InternalError:          "InternalError",
	Afc_Err_MuxError:               "MuxError",
	Afc_Err_NoMemory:               "NoMemory",
	Afc_Err_NotEnoughData:          "NotEnoughData",
	Afc_Err_DirNotEmpty:            "DirNotEmpty",
}

// getError converts an AFC error code to a Go error
func getError(errorCode uint64) error {
	if errorMsg, ok := errorMessages[errorCode]; ok {
		return errors.New(errorMsg)
	}
	return nil
}

type AfcPacketHeader struct {
	Magic         uint64
	Entire_length uint64
	This_length   uint64
	Packet_num    uint64
	Operation     uint64
}

type AfcPacket struct {
	Header        AfcPacketHeader
	HeaderPayload []byte
	Payload       []byte
}

func Decode(reader io.Reader) (AfcPacket, error) {
	var header AfcPacketHeader
	err := binary.Read(reader, binary.LittleEndian, &header)
	if err != nil {
		return AfcPacket{}, err
	}
	if header.Magic != Afc_magic {
		return AfcPacket{}, fmt.Errorf("Wrong magic:%x expected: %x", header.Magic, Afc_magic)
	}
	headerPayloadLength := header.This_length - Afc_header_size
	headerPayload := make([]byte, headerPayloadLength)
	_, err = io.ReadFull(reader, headerPayload)
	if err != nil {
		return AfcPacket{}, err
	}
	contentPayloadLength := header.Entire_length - header.This_length
	payload := make([]byte, contentPayloadLength)
	_, err = io.ReadFull(reader, payload)
	if err != nil {
		return AfcPacket{}, err
	}
	return AfcPacket{header, headerPayload, payload}, nil
}

func Encode(packet AfcPacket, writer io.Writer) error {
	err := binary.Write(writer, binary.LittleEndian, packet.Header)
	if err != nil {
		return err
	}
	_, err = writer.Write(packet.HeaderPayload)
	if err != nil {
		return err
	}

	_, err = writer.Write(packet.Payload)
	if err != nil {
		return err
	}
	return nil
}
