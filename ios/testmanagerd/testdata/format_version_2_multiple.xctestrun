<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CodeCoverageBuildableInfos</key>
	<array>
		<dict>
			<key>Architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>BuildableIdentifier</key>
			<string>506AF5D12D429D9E008E829B:primary</string>
			<key>IncludeInReport</key>
			<true/>
			<key>IsStatic</key>
			<false/>
			<key>Name</key>
			<string>FakeCounterApp.app</string>
			<key>ProductPaths</key>
			<array>
				<string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app/FakeCounterApp</string>
			</array>
			<key>SourceFiles</key>
			<array>
				<string>CounterView.swift</string>
				<string>CounterViewModel.swift</string>
				<string>FakeCounterApp.swift</string>
			</array>
			<key>SourceFilesCommonPathPrefix</key>
			<string>/Users/<USER>/Desktop/apps/rdc-xcuitest-test/app_sources/FakeCounterApp/FakeCounterApp/</string>
			<key>Toolchains</key>
			<array>
				<string>com.apple.dt.toolchain.XcodeDefault</string>
			</array>
		</dict>
		<dict>
			<key>Architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>BuildableIdentifier</key>
			<string>506AF5E12D429DA0008E829B:primary</string>
			<key>IncludeInReport</key>
			<true/>
			<key>IsStatic</key>
			<false/>
			<key>Name</key>
			<string>FakeCounterAppTests.xctest</string>
			<key>ProductPaths</key>
			<array>
				<string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app/PlugIns/FakeCounterAppTests.xctest/FakeCounterAppTests</string>
			</array>
			<key>SourceFiles</key>
			<array>
				<string>CounterXCTests.swift</string>
				<string>SkippedTests.swift</string>
			</array>
			<key>SourceFilesCommonPathPrefix</key>
			<string>/Users/<USER>/Desktop/apps/rdc-xcuitest-test/app_sources/FakeCounterApp/FakeCounterAppXCTests/</string>
			<key>Toolchains</key>
			<array>
				<string>com.apple.dt.toolchain.XcodeDefault</string>
			</array>
		</dict>
		<dict>
			<key>Architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>BuildableIdentifier</key>
			<string>506AF5EB2D429DA0008E829B:primary</string>
			<key>IncludeInReport</key>
			<true/>
			<key>IsStatic</key>
			<false/>
			<key>Name</key>
			<string>FakeCounterAppUITests.xctest</string>
			<key>ProductPaths</key>
			<array>
				<string>__TESTROOT__/Debug-iphoneos/FakeCounterAppUITests-Runner.app/PlugIns/FakeCounterAppUITests.xctest/FakeCounterAppUITests</string>
			</array>
			<key>SourceFiles</key>
			<array>
				<string>/Users/<USER>/Desktop/apps/rdc-xcuitest-test/app_sources/FakeCounterApp/FakeCounterAppUITests/CounterUITests.swift</string>
			</array>
			<key>Toolchains</key>
			<array>
				<string>com.apple.dt.toolchain.XcodeDefault</string>
			</array>
		</dict>
		<dict>
			<key>Architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>BuildableIdentifier</key>
			<string>0B27D3692D562B7500514D89:primary</string>
			<key>IncludeInReport</key>
			<true/>
			<key>IsStatic</key>
			<false/>
			<key>Name</key>
			<string>FakeCounterDuplicateApp.app</string>
			<key>ProductPaths</key>
			<array>
				<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app/FakeCounterDuplicateApp</string>
			</array>
			<key>SourceFiles</key>
			<array>
				<string>CounterView.swift</string>
				<string>CounterViewModel.swift</string>
				<string>FakeCounterApp.swift</string>
			</array>
			<key>SourceFilesCommonPathPrefix</key>
			<string>/Users/<USER>/Desktop/apps/rdc-xcuitest-test/app_sources/FakeCounterApp/FakeCounterDuplicateApp/Duplicate</string>
			<key>Toolchains</key>
			<array>
				<string>com.apple.dt.toolchain.XcodeDefault</string>
			</array>
		</dict>
		<dict>
			<key>Architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>BuildableIdentifier</key>
			<string>0B27D37D2D562B7700514D89:primary</string>
			<key>IncludeInReport</key>
			<true/>
			<key>IsStatic</key>
			<false/>
			<key>Name</key>
			<string>FakeCounterDuplicateAppTests.xctest</string>
			<key>ProductPaths</key>
			<array>
				<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app/PlugIns/FakeCounterDuplicateAppTests.xctest/FakeCounterDuplicateAppTests</string>
			</array>
			<key>SourceFiles</key>
			<array>
				<string>/Users/<USER>/Desktop/apps/rdc-xcuitest-test/app_sources/FakeCounterApp/FakeCounterDuplicateAppTests/FakeCounterDuplicateAppTests.swift</string>
			</array>
			<key>Toolchains</key>
			<array>
				<string>com.apple.dt.toolchain.XcodeDefault</string>
			</array>
		</dict>
		<dict>
			<key>Architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>BuildableIdentifier</key>
			<string>0B27D3872D562B7700514D89:primary</string>
			<key>IncludeInReport</key>
			<true/>
			<key>IsStatic</key>
			<false/>
			<key>Name</key>
			<string>FakeCounterDuplicateAppUITests.xctest</string>
			<key>ProductPaths</key>
			<array>
				<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateAppUITests-Runner.app/PlugIns/FakeCounterDuplicateAppUITests.xctest/FakeCounterDuplicateAppUITests</string>
			</array>
			<key>SourceFiles</key>
			<array>
				<string>/Users/<USER>/Desktop/apps/rdc-xcuitest-test/app_sources/FakeCounterApp/FakeCounterDuplicateAppUITests/FakeCounterDuplicateAppUITests.swift</string>
			</array>
			<key>Toolchains</key>
			<array>
				<string>com.apple.dt.toolchain.XcodeDefault</string>
			</array>
		</dict>
		<dict>
			<key>Architectures</key>
			<array>
				<string>arm64</string>
			</array>
			<key>BuildableIdentifier</key>
			<string>0B19113F2D5C9B0900D7A67B:primary</string>
			<key>IncludeInReport</key>
			<true/>
			<key>IsStatic</key>
			<false/>
			<key>Name</key>
			<string>NoTestTargetAppUITests.xctest</string>
			<key>ProductPaths</key>
			<array>
				<string>__TESTROOT__/Debug-iphoneos/NoTestTargetAppUITests-Runner.app/PlugIns/NoTestTargetAppUITests.xctest/NoTestTargetAppUITests</string>
			</array>
			<key>SourceFiles</key>
			<array>
				<string>/Users/<USER>/Desktop/apps/rdc-xcuitest-test/app_sources/FakeCounterApp/NoTestTargetAppUITests/NoTestTargetAppUITests.swift</string>
			</array>
			<key>Toolchains</key>
			<array>
				<string>com.apple.dt.toolchain.XcodeDefault</string>
			</array>
		</dict>
	</array>
	<key>ContainerInfo</key>
	<dict>
		<key>ContainerName</key>
		<string>FakeCounterApp</string>
		<key>SchemeName</key>
		<string>FakeCounterAppTest</string>
	</dict>
	<key>TestConfigurations</key>
	<array>
		<dict>
			<key>Name</key>
			<string>TestCounterApp_1</string>
			<key>TestTargets</key>
			<array>
				<dict>
					<key>BlueprintName</key>
					<string>FakeCounterAppUITests</string>
					<key>BlueprintProviderName</key>
					<string>FakeCounterApp</string>
					<key>BlueprintProviderRelativePath</key>
					<string>FakeCounterApp.xcodeproj</string>
					<key>BundleIdentifiersForCrashReportEmphasis</key>
					<array>
						<string>saucelabs.FakeCounterApp</string>
						<string>saucelabs.FakeCounterAppUITests</string>
						<string>saucelabs.FakeCounterDuplicateApp</string>
						<string>saucelabs.FakeCounterDuplicateAppTests</string>
						<string>saucelabs.FakeCounterDuplicateAppUITests</string>
						<string>saucelabs.NoTestTargetAppUITests</string>
					</array>
					<key>ClangProfileDataDirectoryPath</key>
					<string>__DERIVEDDATA__/Build/ProfileData</string>
					<key>CommandLineArguments</key>
					<array/>
					<key>DefaultTestExecutionTimeAllowance</key>
					<integer>600</integer>
					<key>DependentProductPaths</key>
					<array>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app/PlugIns/FakeCounterAppTests.xctest</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterAppUITests-Runner.app</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterAppUITests-Runner.app/PlugIns/FakeCounterAppUITests.xctest</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app/PlugIns/FakeCounterDuplicateAppTests.xctest</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateAppUITests-Runner.app</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateAppUITests-Runner.app/PlugIns/FakeCounterDuplicateAppUITests.xctest</string>
						<string>__TESTROOT__/Debug-iphoneos/NoTestTargetAppUITests-Runner.app</string>
						<string>__TESTROOT__/Debug-iphoneos/NoTestTargetAppUITests-Runner.app/PlugIns/NoTestTargetAppUITests.xctest</string>
					</array>
					<key>DiagnosticCollectionPolicy</key>
					<integer>1</integer>
					<key>EnvironmentVariables</key>
					<dict>
						<key>APP_DISTRIBUTOR_ID_OVERRIDE</key>
						<string>com.apple.AppStore</string>
						<key>OS_ACTIVITY_DT_MODE</key>
						<string>YES</string>
						<key>SQLITE_ENABLE_THREAD_ASSERTIONS</key>
						<string>1</string>
						<key>TERM</key>
						<string>dumb</string>
					</dict>
					<key>IsUITestBundle</key>
					<true/>
					<key>IsXCTRunnerHostedTestBundle</key>
					<true/>
					<key>ParallelizationEnabled</key>
					<true/>
					<key>PreferredScreenCaptureFormat</key>
					<string>screenRecording</string>
					<key>ProductModuleName</key>
					<string>FakeCounterAppUITests</string>
					<key>SystemAttachmentLifetime</key>
					<string>deleteOnSuccess</string>
					<key>TestBundlePath</key>
					<string>__TESTHOST__/PlugIns/FakeCounterAppUITests.xctest</string>
					<key>TestHostBundleIdentifier</key>
					<string>saucelabs.FakeCounterAppUITests.xctrunner</string>
					<key>TestHostPath</key>
					<string>__TESTROOT__/Debug-iphoneos/FakeCounterAppUITests-Runner.app</string>
					<key>TestLanguage</key>
					<string></string>
					<key>TestRegion</key>
					<string></string>
					<key>TestTimeoutsEnabled</key>
					<false/>
					<key>TestingEnvironmentVariables</key>
					<dict/>
					<key>ToolchainsSettingValue</key>
					<array/>
					<key>UITargetAppCommandLineArguments</key>
					<array/>
					<key>UITargetAppEnvironmentVariables</key>
					<dict>
						<key>APP_DISTRIBUTOR_ID_OVERRIDE</key>
						<string>com.apple.AppStore</string>
					</dict>
					<key>UITargetAppPath</key>
					<string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app</string>
					<key>UserAttachmentLifetime</key>
					<string>deleteOnSuccess</string>
				</dict>
				<dict>
					<key>BlueprintName</key>
					<string>FakeCounterAppTests</string>
					<key>BlueprintProviderName</key>
					<string>FakeCounterApp</string>
					<key>BlueprintProviderRelativePath</key>
					<string>FakeCounterApp.xcodeproj</string>
					<key>BundleIdentifiersForCrashReportEmphasis</key>
					<array>
						<string>saucelabs.FakeCounterApp</string>
						<string>saucelabs.FakeCounterAppUITests</string>
						<string>saucelabs.FakeCounterDuplicateApp</string>
						<string>saucelabs.FakeCounterDuplicateAppTests</string>
						<string>saucelabs.FakeCounterDuplicateAppUITests</string>
						<string>saucelabs.NoTestTargetAppUITests</string>
					</array>
					<key>ClangProfileDataDirectoryPath</key>
					<string>__DERIVEDDATA__/Build/ProfileData</string>
					<key>CommandLineArguments</key>
					<array/>
					<key>DefaultTestExecutionTimeAllowance</key>
					<integer>600</integer>
					<key>DependentProductPaths</key>
					<array>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app/PlugIns/FakeCounterAppTests.xctest</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterAppUITests-Runner.app</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterAppUITests-Runner.app/PlugIns/FakeCounterAppUITests.xctest</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app/PlugIns/FakeCounterDuplicateAppTests.xctest</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateAppUITests-Runner.app</string>
						<string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateAppUITests-Runner.app/PlugIns/FakeCounterDuplicateAppUITests.xctest</string>
						<string>__TESTROOT__/Debug-iphoneos/NoTestTargetAppUITests-Runner.app</string>
						<string>__TESTROOT__/Debug-iphoneos/NoTestTargetAppUITests-Runner.app/PlugIns/NoTestTargetAppUITests.xctest</string>
					</array>
					<key>DiagnosticCollectionPolicy</key>
					<integer>1</integer>
					<key>EnvironmentVariables</key>
					<dict>
						<key>APP_DISTRIBUTOR_ID_OVERRIDE</key>
						<string>com.apple.AppStore</string>
						<key>OS_ACTIVITY_DT_MODE</key>
						<string>YES</string>
						<key>SQLITE_ENABLE_THREAD_ASSERTIONS</key>
						<string>1</string>
						<key>TERM</key>
						<string>dumb</string>
					</dict>
					<key>IsAppHostedTestBundle</key>
					<true/>
					<key>ParallelizationEnabled</key>
					<true/>
					<key>PreferredScreenCaptureFormat</key>
					<string>screenRecording</string>
					<key>ProductModuleName</key>
					<string>FakeCounterAppTests</string>
					<key>SkipTestIdentifiers</key>
					<array>
						<string>SkippedTests</string>
						<string>SkippedTests/testThatAlwaysFailsAndShouldBeSkipped</string>
					</array>
					<key>SystemAttachmentLifetime</key>
					<string>deleteOnSuccess</string>
					<key>TestBundlePath</key>
					<string>__TESTHOST__/PlugIns/FakeCounterAppTests.xctest</string>
					<key>TestHostBundleIdentifier</key>
					<string>saucelabs.FakeCounterApp</string>
					<key>TestHostPath</key>
					<string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app</string>
					<key>TestLanguage</key>
					<string></string>
					<key>TestRegion</key>
					<string></string>
					<key>TestTimeoutsEnabled</key>
					<false/>
					<key>TestingEnvironmentVariables</key>
					<dict>
						<key>DYLD_INSERT_LIBRARIES</key>
						<string>__TESTHOST__/Frameworks/libXCTestBundleInject.dylib</string>
						<key>XCInjectBundleInto</key>
						<string>unused</string>
					</dict>
					<key>ToolchainsSettingValue</key>
					<array/>
					<key>UserAttachmentLifetime</key>
					<string>deleteOnSuccess</string>
				</dict>
			</array>
		</dict>
		<dict>
		    <key>Name</key>
            <string>TestDuplicateApp_2</string>
            <key>TestTargets</key>
            <array>
                <dict>
                    <key>BlueprintName</key>
                    <string>FakeCounterDuplicateAppTests</string>
                    <key>BlueprintProviderName</key>
                    <string>FakeCounterApp</string>
                    <key>BlueprintProviderRelativePath</key>
                    <string>FakeCounterApp.xcodeproj</string>
                    <key>BundleIdentifiersForCrashReportEmphasis</key>
                    <array>
                        <string>saucelabs.FakeCounterApp</string>
                        <string>saucelabs.FakeCounterAppUITests</string>
                        <string>saucelabs.FakeCounterDuplicateApp</string>
                        <string>saucelabs.FakeCounterDuplicateAppTests</string>
                        <string>saucelabs.FakeCounterDuplicateAppUITests</string>
                        <string>saucelabs.NoTestTargetAppUITests</string>
                    </array>
                    <key>ClangProfileDataDirectoryPath</key>
                    <string>__DERIVEDDATA__/Build/ProfileData</string>
                    <key>CommandLineArguments</key>
                    <array/>
                    <key>DefaultTestExecutionTimeAllowance</key>
                    <integer>600</integer>
                    <key>DependentProductPaths</key>
                    <array>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app/PlugIns/FakeCounterAppTests.xctest</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterAppUITests-Runner.app</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterAppUITests-Runner.app/PlugIns/FakeCounterAppUITests.xctest</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app/PlugIns/FakeCounterDuplicateAppTests.xctest</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateAppUITests-Runner.app</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateAppUITests-Runner.app/PlugIns/FakeCounterDuplicateAppUITests.xctest</string>
                        <string>__TESTROOT__/Debug-iphoneos/NoTestTargetAppUITests-Runner.app</string>
                        <string>__TESTROOT__/Debug-iphoneos/NoTestTargetAppUITests-Runner.app/PlugIns/NoTestTargetAppUITests.xctest</string>
                    </array>
                    <key>DiagnosticCollectionPolicy</key>
                    <integer>1</integer>
                    <key>EnvironmentVariables</key>
                    <dict>
                        <key>APP_DISTRIBUTOR_ID_OVERRIDE</key>
                        <string>com.apple.AppStore</string>
                        <key>OS_ACTIVITY_DT_MODE</key>
                        <string>YES</string>
                        <key>SQLITE_ENABLE_THREAD_ASSERTIONS</key>
                        <string>1</string>
                        <key>TERM</key>
                        <string>dumb</string>
                    </dict>
                    <key>IsAppHostedTestBundle</key>
                    <true/>
                    <key>PreferredScreenCaptureFormat</key>
                    <string>screenRecording</string>
                    <key>ProductModuleName</key>
                    <string>FakeCounterDuplicateAppTests</string>
                    <key>SystemAttachmentLifetime</key>
                    <string>deleteOnSuccess</string>
                    <key>TestBundlePath</key>
                    <string>__TESTHOST__/PlugIns/FakeCounterDuplicateAppTests.xctest</string>
                    <key>TestHostBundleIdentifier</key>
                    <string>saucelabs.FakeCounterDuplicateApp</string>
                    <key>TestHostPath</key>
                    <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app</string>
                    <key>TestLanguage</key>
                    <string></string>
                    <key>TestRegion</key>
                    <string></string>
                    <key>TestTimeoutsEnabled</key>
                    <false/>
                    <key>TestingEnvironmentVariables</key>
                    <dict>
                        <key>DYLD_INSERT_LIBRARIES</key>
                        <string>__TESTHOST__/Frameworks/libXCTestBundleInject.dylib</string>
                        <key>XCInjectBundleInto</key>
                        <string>unused</string>
                    </dict>
                    <key>ToolchainsSettingValue</key>
                    <array/>
                    <key>UserAttachmentLifetime</key>
                    <string>deleteOnSuccess</string>
                </dict>
                <dict>
                    <key>BlueprintName</key>
                    <string>FakeCounterDuplicateAppUITests</string>
                    <key>BlueprintProviderName</key>
                    <string>FakeCounterApp</string>
                    <key>BlueprintProviderRelativePath</key>
                    <string>FakeCounterApp.xcodeproj</string>
                    <key>BundleIdentifiersForCrashReportEmphasis</key>
                    <array>
                        <string>saucelabs.FakeCounterApp</string>
                        <string>saucelabs.FakeCounterAppUITests</string>
                        <string>saucelabs.FakeCounterDuplicateApp</string>
                        <string>saucelabs.FakeCounterDuplicateAppTests</string>
                        <string>saucelabs.FakeCounterDuplicateAppUITests</string>
                        <string>saucelabs.NoTestTargetAppUITests</string>
                    </array>
                    <key>ClangProfileDataDirectoryPath</key>
                    <string>__DERIVEDDATA__/Build/ProfileData</string>
                    <key>CommandLineArguments</key>
                    <array/>
                    <key>DefaultTestExecutionTimeAllowance</key>
                    <integer>600</integer>
                    <key>DependentProductPaths</key>
                    <array>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterApp.app/PlugIns/FakeCounterAppTests.xctest</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterAppUITests-Runner.app</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterAppUITests-Runner.app/PlugIns/FakeCounterAppUITests.xctest</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app/PlugIns/FakeCounterDuplicateAppTests.xctest</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateAppUITests-Runner.app</string>
                        <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateAppUITests-Runner.app/PlugIns/FakeCounterDuplicateAppUITests.xctest</string>
                        <string>__TESTROOT__/Debug-iphoneos/NoTestTargetAppUITests-Runner.app</string>
                        <string>__TESTROOT__/Debug-iphoneos/NoTestTargetAppUITests-Runner.app/PlugIns/NoTestTargetAppUITests.xctest</string>
                    </array>
                    <key>DiagnosticCollectionPolicy</key>
                    <integer>1</integer>
                    <key>EnvironmentVariables</key>
                    <dict>
                        <key>APP_DISTRIBUTOR_ID_OVERRIDE</key>
                        <string>com.apple.AppStore</string>
                        <key>OS_ACTIVITY_DT_MODE</key>
                        <string>YES</string>
                        <key>SQLITE_ENABLE_THREAD_ASSERTIONS</key>
                        <string>1</string>
                        <key>TERM</key>
                        <string>dumb</string>
                    </dict>
                    <key>IsUITestBundle</key>
                    <true/>
                    <key>IsXCTRunnerHostedTestBundle</key>
                    <true/>
                    <key>PreferredScreenCaptureFormat</key>
                    <string>screenRecording</string>
                    <key>ProductModuleName</key>
                    <string>FakeCounterDuplicateAppUITests</string>
                    <key>SystemAttachmentLifetime</key>
                    <string>deleteOnSuccess</string>
                    <key>TestBundlePath</key>
                    <string>__TESTHOST__/PlugIns/FakeCounterDuplicateAppUITests.xctest</string>
                    <key>TestHostBundleIdentifier</key>
                    <string>saucelabs.FakeCounterDuplicateAppUITests.xctrunner</string>
                    <key>TestHostPath</key>
                    <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateAppUITests-Runner.app</string>
                    <key>TestLanguage</key>
                    <string></string>
                    <key>TestRegion</key>
                    <string></string>
                    <key>TestTimeoutsEnabled</key>
                    <false/>
                    <key>TestingEnvironmentVariables</key>
                    <dict/>
                    <key>ToolchainsSettingValue</key>
                    <array/>
                    <key>UITargetAppCommandLineArguments</key>
                    <array/>
                    <key>UITargetAppEnvironmentVariables</key>
                    <dict>
                        <key>APP_DISTRIBUTOR_ID_OVERRIDE</key>
                        <string>com.apple.AppStore</string>
                    </dict>
                    <key>UITargetAppPath</key>
                    <string>__TESTROOT__/Debug-iphoneos/FakeCounterDuplicateApp.app</string>
                    <key>UserAttachmentLifetime</key>
                    <string>deleteOnSuccess</string>
                </dict>
            </array>
		</dict>
	</array>
	<key>TestPlan</key>
	<dict>
		<key>IsDefault</key>
		<true/>
		<key>Name</key>
		<string>FakeAppTestPlan</string>
	</dict>
	<key>__xctestrun_metadata__</key>
	<dict>
		<key>FormatVersion</key>
		<integer>2</integer>
	</dict>
</dict>
</plist>
