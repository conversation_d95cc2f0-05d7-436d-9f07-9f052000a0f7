package nskeyedarchiver

import (
	"fmt"
	"regexp"
	"sync"
	"time"

	"github.com/Masterminds/semver"
	"github.com/google/uuid"
	"howett.net/plist"
)

const (
	attachmentLifetimeKeepAlways      = 0
	attachmentLifetimeDeleteOnSuccess = 1
	attachmentLifetimeDeleteAlways    = 2
)

// decodableClassesMap 和 encodableClassesMap 用于存储类名到处理函数的映射
var (
	decodableClassesMap map[string]func(map[string]any, []any) any
	encodableClassesMap map[string]func(object any, objects []any) ([]any, plist.UID)
	once                sync.Once
)

// initClassMaps 初始化类映射，避免初始化循环
func initClassMaps() {
	once.Do(
		func() {
			decodableClassesMap = map[string]func(map[string]any, []any) any{
				"DTActivityTraceTapMessage": NewDTActivityTraceTapMessage,
				"DTSysmonTapMessage":        NewDTActivityTraceTapMessage,
				"NSError":                   NewNSError,
				"NSNull":                    NewNSNullFromArchived,
				"NSDate":                    NewNSDate,
				"XCTestConfiguration":       NewXCTestConfigurationFromBytes,
				"DTTapHeartbeatMessage":     NewDTTapHeartbeatMessage,
				"XCTCapabilities":           NewXCTCapabilities,
				"NSUUID":                    NewNSUUIDFromBytes,
				"XCActivityRecord":          NewXCActivityRecord,
				"XCTAttachment":             NewXCTAttachment,
				"DTKTraceTapMessage":        NewDTKTraceTapMessage,
				"NSValue":                   NewNSValue,
				"NSArray":                   NewNSArray,
				"XCTTestIdentifier":         NewXCTTestIdentifier,
				"DTTapStatusMessage":        NewDTTapStatusMessage,
				"DTTapMessage":              NewDTTapMessage,
				"DTCPUClusterInfo":          NewDTCPUClusterInfo,
				"XCTIssue":                  NewXCTIssue,
				"XCTMutableIssue":           NewXCTIssue,
				"XCTSourceCodeContext":      NewXCTSourceCodeContext,
				"XCTSourceCodeLocation":     NewXCTSourceCodeLocation,
				"NSMutableData":             NewNSMutableData,
			}

			encodableClassesMap = map[string]func(object any, objects []any) ([]any, plist.UID){
				"XCTestConfiguration":  archiveXcTestConfiguration,
				"NSUUID":               archiveNSUUID,
				"NSURL":                archiveNSURL,
				"NSNull":               archiveNSNull,
				"NSMutableDictionary":  archiveNSMutableDictionary,
				"XCTCapabilities":      archiveXCTCapabilities,
				"[]string":             archiveStringSlice,
				"NSArray":              archiveNSArray,
				"NSMutableArray":       archiveNSMutableArray,
				"NSSet":                archiveNSSet,
				"XCTTestIdentifier":    archiveXCTTestIdentifier,
				"XCTTestIdentifierSet": archiveXCTTestIdentifierSet,
			}
		},
	)
}

// GetDecodableClass 返回指定类名的解码函数
func GetDecodableClass(className string) func(map[string]any, []any) any {
	initClassMaps()
	return decodableClassesMap[className]
}

// GetEncodableClass 返回指定类名的编码函数
func GetEncodableClass(className string) func(object any, objects []any) ([]any, plist.UID) {
	initClassMaps()
	return encodableClassesMap[className]
}

var testIdentifierRegex = regexp.MustCompile(`((?P<module>[^\.]+)\.)?(?P<class>[^\/]+)(\/(?P<method>[^\.]+))?`)

type XCTestConfiguration struct {
	contents map[string]any
}

func NewXCTestConfiguration(
	productModuleName string,
	sessionIdentifier uuid.UUID,
	targetApplicationBundleID string,
	targetApplicationPath string,
	testBundleURL string,
	testsToRun []string,
	testsToSkip []string,
	isXCTest bool,
	version *semver.Version,
) XCTestConfiguration {
	contents := map[string]any{}

	var testsToRunEntry any
	testsToRunEntry = plist.UID(0)
	if testsToRun != nil {
		testsToRunEntry = createTestsSet(testsToRun)
	}

	var testsToSkipEntry any
	testsToSkipEntry = plist.UID(0)
	if testsToSkip != nil {
		testsToSkipEntry = createTestsSet(testsToSkip)
	}

	var testIdentifiersToRunEntry any
	testIdentifiersToRunEntry = plist.UID(0)
	if testsToRun != nil {
		testIdentifiersToRunEntry = createTestIdentifierSet(testsToRun)
	}

	var testIdentifiersToSkipEntry any
	testIdentifiersToSkipEntry = plist.UID(0)
	if testsToSkip != nil {
		testIdentifiersToSkipEntry = createTestIdentifierSet(testsToSkip)
	}

	appUnderTestExists := targetApplicationPath != "" && targetApplicationBundleID != ""
	if appUnderTestExists {
		contents["productModuleName"] = productModuleName
		contents["targetApplicationBundleID"] = targetApplicationBundleID
		contents["targetApplicationPath"] = targetApplicationPath
	}

	contents["aggregateStatisticsBeforeCrash"] = map[string]any{"XCSuiteRecordsKey": map[string]any{}}
	if version.Major() >= 17 {
		contents["automationFrameworkPath"] = "/System/Developer/Library/PrivateFrameworks/XCTAutomationSupport.framework"
	} else {
		contents["automationFrameworkPath"] = "/Developer/Library/PrivateFrameworks/XCTAutomationSupport.framework"
	}
	contents["baselineFileRelativePath"] = plist.UID(0)
	contents["baselineFileURL"] = plist.UID(0)
	contents["defaultTestExecutionTimeAllowance"] = plist.UID(0)
	contents["disablePerformanceMetrics"] = false
	contents["emitOSLogs"] = false
	// contents["formatVersion"] = 2
	contents["gatherLocalizableStringsData"] = false
	contents["initializeForUITesting"] = !isXCTest
	contents["maximumTestExecutionTimeAllowance"] = plist.UID(0)
	contents["randomExecutionOrderingSeed"] = plist.UID(0)
	contents["reportActivities"] = true
	contents["reportResultsToIDE"] = true
	contents["sessionIdentifier"] = NewNSUUID(sessionIdentifier)
	contents["systemAttachmentLifetime"] = attachmentLifetimeDeleteAlways
	// testApplicationDependencies
	contents["testApplicationUserOverrides"] = plist.UID(0)
	contents["testBundleRelativePath"] = plist.UID(0)
	contents["testBundleURL"] = NewNSURL(testBundleURL)
	contents["testExecutionOrdering"] = 0
	contents["testsDrivenByIDE"] = false
	contents["testsMustRunOnMainThread"] = true
	contents["testTimeoutsEnabled"] = false
	contents["treatMissingBaselinesAsFailures"] = false
	contents["userAttachmentLifetime"] = attachmentLifetimeKeepAlways
	contents["preferredScreenCaptureFormat"] = 2
	contents["IDECapabilities"] = XCTCapabilities{
		CapabilitiesDictionary: map[string]any{
			"expected failure test capability":         true,
			"test case run configurations":             true,
			"test timeout capability":                  true,
			"test iterations":                          true,
			"request diagnostics for specific devices": true,
			"delayed attachment transfer":              true,
			"skipped test capability":                  true,
			"daemon container sandbox extension":       true,
			"ubiquitous test identifiers":              true,
			"XCTIssue capability":                      true,
		},
	}

	if testIdentifiersToRunEntry != plist.UID(0) {
		contents["testsToRun"] = testsToRunEntry
		contents["testIdentifiersToRun"] = testIdentifiersToRunEntry
	}

	if testIdentifiersToSkipEntry != plist.UID(0) {
		contents["testsToSkip"] = testsToSkipEntry
		contents["testIdentifiersToSkip"] = testIdentifiersToSkipEntry
	}

	return XCTestConfiguration{contents}
}

func createTestIdentifierSet(tests []string) XCTTestIdentifierSet {
	testsIdentifiersConfig := make([]XCTTestIdentifier, 0, len(tests))
	for _, t := range tests {
		match := testIdentifierRegex.FindStringSubmatch(t)
		matchedGroups := make(map[string]string)
		for i, name := range testIdentifierRegex.SubexpNames() {
			if i != 0 && name != "" {
				matchedGroups[name] = match[i]
			}
		}

		components := make([]string, 0, 2)

		// the `module` parameter is ingored here as it only works reliably with `testIdentifiersToRun`
		// adding the `module` parameter to `testIdentifiersToSkip` won't skip the specified tests there
		// this was verified with Xcode 15
		_ = matchedGroups["module"]
		clazz := matchedGroups["class"]
		method := matchedGroups["method"]

		options := uint64(3)
		components = append(components, clazz)
		if len(method) > 0 {
			options = 2
			components = append(components, method)
		}

		testsIdentifiersConfig = append(
			testsIdentifiersConfig, XCTTestIdentifier{
				O: options,
				C: components,
			},
		)
	}

	testArray := NSMutableArray{
		Values: toInterfaceSliceOfTests(testsIdentifiersConfig),
	}

	return XCTTestIdentifierSet{Identifiers: testArray}
}

func createTestsSet(tests []string) NSSet {
	return NSSet{Objects: toInterfaceSlice(tests)}
}

func archiveXcTestConfiguration(xctestconfigInterface any, objects []any) ([]any, plist.UID) {
	xctestconfig := xctestconfigInterface.(XCTestConfiguration)
	xcconfigRef := plist.UID(len(objects))
	objects = append(objects, xctestconfig.contents)
	classRef := plist.UID(len(objects))
	objects = append(objects, buildClassDict("XCTestConfiguration", "NSObject"))

	xctestconfig.contents["$class"] = classRef

	for _, key := range []string{
		"aggregateStatisticsBeforeCrash", "automationFrameworkPath", "productModuleName", "sessionIdentifier",
		"targetApplicationBundleID", "targetApplicationPath", "testBundleURL", "testsToRun", "testsToSkip",
		"testIdentifiersToRun", "testIdentifiersToSkip", "IDECapabilities",
	} {
		_, ok := xctestconfig.contents[key]
		if ok {
			if xctestconfig.contents[key] == plist.UID(0) {
				continue // No need to archive NSNull
			}
			var ref plist.UID
			objects, ref = archive(xctestconfig.contents[key], objects)
			xctestconfig.contents[key] = ref
		}
	}

	return objects, xcconfigRef
}

type NSUUID struct {
	uuidbytes []byte
}

func (n NSUUID) String() string {
	uid, err := uuid.FromBytes(n.uuidbytes)
	if err != nil {
		return fmt.Sprintf("Failed converting %x to uuid with %+v", n.uuidbytes, err)
	}
	return uid.String()
}

type XCActivityRecord struct {
	Finish       NSDate
	Start        NSDate
	Title        string
	UUID         NSUUID
	ActivityType string
	Attachments  []XCTAttachment
}

func NewXCActivityRecord(object map[string]any, objects []any) any {
	finish_ref := object["finish"].(plist.UID)
	finish := NSDate{}
	if _, ok := objects[finish_ref].(map[string]any); ok {
		finish_raw := objects[finish_ref].(map[string]any)
		finish = NewNSDate(finish_raw, objects).(NSDate)
	}

	start_ref := object["start"].(plist.UID)
	start := NSDate{}
	if _, ok := objects[start_ref].(map[string]any); ok {
		start_raw := objects[start_ref].(map[string]any)
		start = NewNSDate(start_raw, objects).(NSDate)
	}

	uuid_ref := object["uuid"].(plist.UID)
	uuid_raw := objects[uuid_ref].(map[string]any)
	uuid := NewNSUUIDFromBytes(uuid_raw, objects).(NSUUID)

	title_ref := object["title"].(plist.UID)
	title := objects[title_ref].(string)

	attachments_ref := object["attachments"].(plist.UID)
	attachments_raw := objects[attachments_ref].(map[string]any)

	attachments := make([]XCTAttachment, 0)
	for _, obj := range NewNSArray(attachments_raw, objects).(NSArray).Values {
		attachments = append(attachments, obj.(XCTAttachment))
	}

	activityType_ref := object["activityType"].(plist.UID)
	activityType := objects[activityType_ref].(string)

	return XCActivityRecord{
		Finish: finish, Start: start, UUID: uuid, Title: title, Attachments: attachments, ActivityType: activityType,
	}
}

const (
	LifetimeKeepAlways      = uint64(0)
	LifetimeDeleteOnSuccess = uint64(1)
)

type XCTAttachment struct {
	lifetime              uint64
	UniformTypeIdentifier string
	fileNameOverride      string
	Payload               []uint8
	Timestamp             float64
	Name                  string
	userInfo              map[string]any
}

func NewXCTAttachment(object map[string]any, objects []any) any {
	lifetime := object["lifetime"].(uint64)
	uniformTypeIdentifier := objects[object["uniformTypeIdentifier"].(plist.UID)].(string)
	fileNameOverride := objects[object["fileNameOverride"].(plist.UID)].(string)
	timestamp := objects[object["timestamp"].(plist.UID)].(float64)
	name := objects[object["name"].(plist.UID)].(string)
	userInfo, _ := extractDictionary(objects[object["userInfo"].(plist.UID)].(map[string]any), objects)

	payloadRaw := objects[object["payload"].(plist.UID)]
	payload := extractAttachmentPayload(payloadRaw, objects)

	return XCTAttachment{
		lifetime:              lifetime,
		UniformTypeIdentifier: uniformTypeIdentifier,
		fileNameOverride:      fileNameOverride,
		Payload:               payload,
		Timestamp:             timestamp,
		Name:                  name,
		userInfo:              userInfo,
	}
}

func extractAttachmentPayload(payloadRaw any, objects []any) []uint8 {
	payload, byteSliceOk := payloadRaw.([]uint8)
	if !byteSliceOk {
		mapPayload, mapOk := payloadRaw.(map[string]any)
		if mapOk {
			payloadClassMap, classOk := objects[mapPayload["$class"].(plist.UID)].(map[string]any)
			if classOk {
				payloadClass := payloadClassMap["$classname"]
				if payloadClass == "NSMutableData" || payloadClass == "NSData" {
					payload = NewNSMutableData(mapPayload, objects).([]uint8)
				}
			}
		} else {
			payload = make([]uint8, 0)
		}
	}

	return payload
}

func NewNSUUIDFromBytes(object map[string]any, objects []any) any {
	val := object["NS.uuidbytes"].([]byte)
	return NSUUID{uuidbytes: val}
}

func NewNSUUID(id uuid.UUID) NSUUID {
	bytes, err := id.MarshalBinary()
	if err != nil {
		panic(fmt.Sprintf("Unexpected Error: %v", err))
	}
	return NSUUID{bytes}
}

func archiveNSUUID(uid any, objects []any) ([]any, plist.UID) {
	nsuuid := uid.(NSUUID)
	object := map[string]any{}

	object["NS.uuidbytes"] = nsuuid.uuidbytes
	uuidReference := len(objects)
	objects = append(objects, object)

	classref := uuidReference + 1
	object[class] = plist.UID(classref)
	objects = append(objects, buildClassDict("NSUUID", "NSObject"))

	return objects, plist.UID(uuidReference)
}

func archiveXCTCapabilities(capsIface any, objects []any) ([]any, plist.UID) {
	caps := capsIface.(XCTCapabilities)
	object := map[string]any{}

	objects, dictRef := serializeMap(caps.CapabilitiesDictionary, objects, buildClassDict("NSDictionary", "NSObject"))
	object["capabilities-dictionary"] = dictRef

	capsReference := len(objects)
	objects = append(objects, object)

	classref := capsReference + 1
	object[class] = plist.UID(classref)
	objects = append(objects, buildClassDict("XCTCapabilities", "NSObject"))
	return objects, plist.UID(capsReference)
}

type NSURL struct {
	Path string
}

func NewNSURL(path string) NSURL {
	return NSURL{path}
}

func archiveNSURL(nsurlInterface any, objects []any) ([]any, plist.UID) {
	nsurl := nsurlInterface.(NSURL)
	object := map[string]any{}

	object["NS.base"] = plist.UID(0)

	urlReference := len(objects)
	objects = append(objects, object)

	classref := urlReference + 1
	object[class] = plist.UID(classref)
	objects = append(objects, buildClassDict("NSURL", "NSObject"))

	pathRef := classref + 1
	object["NS.relative"] = plist.UID(pathRef)
	objects = append(objects, fmt.Sprintf("file://%s", nsurl.Path))

	return objects, plist.UID(urlReference)
}

type DTActivityTraceTapMessage struct {
	DTTapMessagePlist map[string]any
}

func NewDTActivityTraceTapMessage(object map[string]any, objects []any) any {
	ref := object["DTTapMessagePlist"].(plist.UID)
	plist, _ := extractDictionary(objects[ref].(map[string]any), objects)
	return DTActivityTraceTapMessage{DTTapMessagePlist: plist}
}

type DTKTraceTapMessage struct {
	DTTapMessagePlist map[string]any
}

func NewDTKTraceTapMessage(object map[string]any, objects []any) any {
	ref := object["DTTapMessagePlist"].(plist.UID)
	plist, _ := extractDictionary(objects[ref].(map[string]any), objects)
	return DTKTraceTapMessage{DTTapMessagePlist: plist}
}

type NSValue struct {
	NSSpecial uint64
	NSRectval string
}

func NewNSValue(object map[string]any, objects []any) any {
	ref := object["NS.rectval"].(plist.UID)
	rectval, _ := objects[ref].(string)
	special := object["NS.special"].(uint64)
	return NSValue{NSRectval: rectval, NSSpecial: special}
}

type NSArray struct {
	Values []any
}

func NewNSArray(object map[string]any, objects []any) any {
	objectRefs := object["NS.objects"].([]any)

	uidList := toUidList(objectRefs)
	extractObjects, _ := extractObjects(uidList, objects)

	return NSArray{Values: extractObjects}
}

func archiveNSArray(object any, objects []any) ([]any, plist.UID) {
	sl := object.(NSArray)
	return serializeArray(sl.Values, objects)
}

type XCTTestIdentifier struct {
	O uint64
	C []string
}

func (x XCTTestIdentifier) String() string {
	return fmt.Sprintf("XCTTestIdentifier{o:%d , c:%v}", x.O, x.C)
}

func NewXCTTestIdentifier(object map[string]any, objects []any) any {
	ref := object["c"].(plist.UID)
	// plist, _ := extractObjects(objects[ref].(map[string]any), objects)
	fd := objects[ref].(map[string]any)
	extractObjects, _ := extractObjects(toUidList(fd[nsObjects].([]any)), objects)
	stringarray := make([]string, len(extractObjects))
	for i, v := range extractObjects {
		stringarray[i] = v.(string)
	}
	o := object["o"].(uint64)
	return XCTTestIdentifier{
		O: o,
		C: stringarray,
	}
}

func archiveXCTTestIdentifier(object any, objects []any) ([]any, plist.UID) {
	testIdentifier := object.(XCTTestIdentifier)

	classRef := len(objects)
	objects = append(objects, buildClassDict("XCTTestIdentifier", "NSObject"))

	objects, cRef := serializeArray(toInterfaceSlice(testIdentifier.C), objects)

	identifierMap := map[string]any{}
	identifierMap["c"] = cRef
	identifierMap["o"] = testIdentifier.O
	identifierMap[class] = plist.UID(classRef)
	ref := len(objects)
	objects = append(objects, identifierMap)

	return objects, plist.UID(ref)
}

type XCTTestIdentifierSet struct {
	Identifiers NSMutableArray
}

func archiveXCTTestIdentifierSet(object any, objects []any) ([]any, plist.UID) {
	identifierSet := object.(XCTTestIdentifierSet)

	objects, arrayRef := archiveNSMutableArray(identifierSet.Identifiers, objects)

	identifierSetMap := map[string]any{}
	ref := len(objects)
	identifierSetMap["identifiers"] = arrayRef
	objects = append(objects, identifierSetMap)

	classRef := ref + 1
	objects = append(objects, buildClassDict("XCTTestIdentifierSet", "NSObject"))
	identifierSetMap[class] = plist.UID(classRef)

	return objects, plist.UID(ref)
}

// TODO: make this nice, partially extracting objects is not really cool
type PartiallyExtractedXcTestConfig struct {
	values map[string]any
}

func NewXCTestConfigurationFromBytes(object map[string]any, objects []any) any {
	config := make(map[string]any, len(object))
	for k, v := range object {
		value := v
		uid, ok := v.(plist.UID)
		if ok {
			value = objects[uid]
		}
		config[k] = value
	}

	return PartiallyExtractedXcTestConfig{config}
}

type NSError struct {
	ErrorCode uint64
	Domain    string
	UserInfo  map[string]any
}

func NewNSError(object map[string]any, objects []any) any {
	errorCode := object["NSCode"].(uint64)
	userInfo_ref := object["NSUserInfo"].(plist.UID)
	domain_ref := object["NSDomain"].(plist.UID)
	domain := objects[domain_ref].(string)
	userinfo, _ := extractDictionary(objects[userInfo_ref].(map[string]any), objects)

	return NSError{ErrorCode: errorCode, Domain: domain, UserInfo: userinfo}
}

func (err NSError) Error() string {
	var description any = "no description available"
	if d, ok := err.UserInfo["NSLocalizedDescription"]; ok {
		description = d
	}
	return fmt.Sprintf("%v (Error code: %d, Domain: %s)", description, err.ErrorCode, err.Domain)
}

// Apples Reference Date is Jan 1st 2001 00:00
const nsReferenceDate = ************

type NSDate struct {
	Timestamp time.Time
}

type DTTapHeartbeatMessage struct {
	DTTapMessagePlist map[string]any
}

type DTTapMessage struct {
	DTTapMessagePlist map[string]any
}

type XCTCapabilities struct {
	CapabilitiesDictionary map[string]any
}

func NewXCTCapabilities(object map[string]any, objects []any) any {
	ref := object["capabilities-dictionary"].(plist.UID)
	plist, _ := extractDictionary(objects[ref].(map[string]any), objects)
	return XCTCapabilities{CapabilitiesDictionary: plist}
}

func NewDTTapHeartbeatMessage(object map[string]any, objects []any) any {
	ref := object["DTTapMessagePlist"].(plist.UID)
	plist, _ := extractDictionary(objects[ref].(map[string]any), objects)
	return DTTapHeartbeatMessage{DTTapMessagePlist: plist}
}

func NewDTTapMessage(object map[string]any, objects []any) any {
	ref := object["DTTapMessagePlist"].(plist.UID)
	plist, _ := extractDictionary(objects[ref].(map[string]any), objects)
	return DTTapMessage{DTTapMessagePlist: plist}
}

type DTTapStatusMessage struct {
	DTTapMessagePlist map[string]any
}

func NewDTTapStatusMessage(object map[string]any, objects []any) any {
	ref := object["DTTapMessagePlist"].(plist.UID)
	plist, _ := extractDictionary(objects[ref].(map[string]any), objects)
	return DTTapStatusMessage{DTTapMessagePlist: plist}
}

func NewNSDate(object map[string]any, objects []any) any {
	value := object["NS.time"].(float64)
	milliesFloat := (1000*value + nsReferenceDate)
	millies := int64(milliesFloat)
	time := time.Unix(0, millies*int64(time.Millisecond))
	return NSDate{time}
}

func (n NSDate) String() string {
	return n.Timestamp.String()
}

type DTCPUClusterInfo struct {
	ClusterID    uint64
	ClusterFlags uint64
}

func NewDTCPUClusterInfo(object map[string]any, objects []any) any {
	return DTCPUClusterInfo{ClusterID: object["_clusterID"].(uint64), ClusterFlags: object["_clusterFlags"].(uint64)}
}

type NSNull struct {
	class string
}

func NewNSNullFromArchived(object map[string]any, objects []any) any {
	return NewNSNull()
}

func NewNSNull() any {
	return NSNull{"NSNull"}
}

func archiveNSNull(_ any, objects []any) ([]any, plist.UID) {
	nsnull := map[string]any{}
	nsnullReference := len(objects)
	objects = append(objects, nsnull)
	objects = append(objects, buildClassDict("NSNull", "NSObject"))
	nsnull[class] = plist.UID(nsnullReference + 1)
	return objects, plist.UID(nsnullReference)
}

type NSMutableDictionary struct {
	internalDict map[string]any
}

func NewNSMutableDictionary(internalDict map[string]any) any {
	return NSMutableDictionary{internalDict}
}

func archiveStringSlice(object any, objects []any) ([]any, plist.UID) {
	sl := object.([]string)
	return serializeArray(toInterfaceSlice(sl), objects)
}

func archiveNSMutableDictionary(object any, objects []any) ([]any, plist.UID) {
	mut := object.(NSMutableDictionary)
	return serializeMap(mut.internalDict, objects, buildClassDict("NSMutableDictionary", "NSDictionary", "NSObject"))
}

type NSMutableArray struct {
	Values []any
}

func archiveNSMutableArray(object any, objects []any) ([]any, plist.UID) {
	sl := object.(NSMutableArray)
	return serializeMutableArray(sl.Values, objects)
}

type NSSet struct {
	Objects []any
}

func archiveNSSet(set any, objects []any) ([]any, plist.UID) {
	nsset := set.(NSSet)
	return serializeSet(nsset.Objects, objects)
}

type XCTIssue struct {
	RuntimeIssueSeverity uint64
	DetailedDescription  string
	CompactDescription   string
	SourceCodeContext    XCTSourceCodeContext
}

func NewXCTIssue(object map[string]any, objects []any) any {
	runtimeIssueSeverity := object["runtimeIssueSeverity"].(uint64)
	detailedDescriptionRef := object["detailed-description"].(plist.UID)
	sourceCodeContextRef := object["source-code-context"].(plist.UID)
	compactDescriptionRef := object["compact-description"].(plist.UID)

	detailedDescription := objects[detailedDescriptionRef].(string)
	compactDescription := objects[compactDescriptionRef].(string)
	sourceCodeContext := NewXCTSourceCodeContext(
		objects[sourceCodeContextRef].(map[string]any), objects,
	).(XCTSourceCodeContext)

	return XCTIssue{
		RuntimeIssueSeverity: runtimeIssueSeverity, DetailedDescription: detailedDescription,
		CompactDescription: compactDescription, SourceCodeContext: sourceCodeContext,
	}
}

func NewNSMutableData(object map[string]any, objects []any) any {
	data, ok := object["NS.data"].([]uint8)
	if ok {
		return data
	}

	return make([]uint8, 0)
}

type XCTSourceCodeContext struct {
	Location XCTSourceCodeLocation
}

func NewXCTSourceCodeContext(object map[string]any, objects []any) any {
	locationRef := object["location"].(plist.UID)
	location := NewXCTSourceCodeLocation(objects[locationRef].(map[string]any), objects).(XCTSourceCodeLocation)

	return XCTSourceCodeContext{Location: location}
}

type XCTSourceCodeLocation struct {
	FileUrl    NSURL
	LineNumber uint64
}

func NewXCTSourceCodeLocation(object map[string]any, objects []any) any {
	fileUrlRef := object["file-url"].(plist.UID)
	relativeRef := objects[fileUrlRef].(map[string]any)["NS.relative"].(plist.UID)
	relativePath := objects[int(relativeRef)].(string)
	fileUrl := NewNSURL(relativePath)
	lineNumber := object["line-number"].(uint64)

	return XCTSourceCodeLocation{FileUrl: fileUrl, LineNumber: lineNumber}
}

func toInterfaceSliceOfTests(testSlice []XCTTestIdentifier) []any {
	result := make([]any, len(testSlice))
	for i, e := range testSlice {
		result[i] = e
	}
	return result
}
