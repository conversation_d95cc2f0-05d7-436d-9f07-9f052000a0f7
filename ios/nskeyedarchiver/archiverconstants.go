package nskeyedarchiver

const (
	archiverKey     = "$archiver"
	NsKeyedArchiver = "NSKeyedArchiver"
	versionKey      = "$version"
	topKey          = "$top"
	objectsKey      = "$objects"
	nsObjects       = "NS.objects"
	nsKeys          = "NS.keys"
	class           = "$class"
	className       = "$classname"
	versionValue    = 100000
	null            = "$null"
	nsDataKey       = "NS.data"
	nsStringKey     = "NS.string"
	nsNullKey       = "NSNull"
)

const (
	nsArray        = "NSArray"
	nsMutableArray = "NSMutableArray"
	nsSet          = "NSSet"
	nsMutableSet   = "NSMutableSet"
)

const (
	nsDictionary        = "NSDictionary"
	nsMutableDictionary = "NSMutableDictionary"
)

const (
	nsMutableData   = "NSMutableData"
	nsMutableString = "NSMutableString"
)
