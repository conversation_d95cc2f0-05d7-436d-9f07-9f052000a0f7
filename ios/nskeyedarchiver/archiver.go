package nskeyedarchiver

import (
	"fmt"
	"reflect"

	"howett.net/plist"
)

/*
type NSKeyedObject struct {
	isPrimitive bool
	primitive   any
}
*/

func ArchiveXML(object any) (string, error) {
	plist := archiveObject(object)
	return toPlist(plist)
}

func ArchiveBin(object any) ([]byte, error) {
	plist := archiveObject(object)
	return toBinaryPlist(plist)
}

func archiveObject(object any) any {
	archiverSkeleton := createSkeleton(true)

	objects := make([]any, 1)
	objects[0] = null
	objects, pid := archive(object, objects)
	archiverSkeleton[topKey] = map[string]any{"root": pid}

	archiverSkeleton[objectsKey] = objects
	return archiverSkeleton
}

func createSkeleton(withRoot bool) map[string]any {
	var topDict map[string]any
	if withRoot {
		topDict = map[string]any{"root": plist.UID(1)}
	}

	return map[string]any{
		versionKey:  versionValue,
		archiverKey: NsKeyedArchiver,
		topKey:      topDict,
	}
}

func archive(object any, objects []any) ([]any, plist.UID) {
	if obj, ok := isPrimitiveObject(object); ok {
		index := len(objects)
		objects = append(objects, obj)
		return objects, plist.UID(index)
	}

	if v, ok := object.([]any); ok {
		return serializeArray(v, objects)
	}

	if v, ok := object.(map[string]any); ok {
		return serializeMap(v, objects, buildClassDict("NSDictionary", "NSObject"))
	}
	typeOf := reflect.TypeOf(object)
	name := typeOf.Name()
	// seems like Name() can be empty for pointer types
	if name == "" {
		name = typeOf.String()
	}

	if encoderFunc := GetEncodableClass(name); encoderFunc != nil {
		return encoderFunc(object, objects)
	}

	panic(fmt.Errorf("NSKeyedArchiver Unsupported object: '%s' of type:%s", object, typeOf))
}

func serializeArray(array, objects []any) ([]any, plist.UID) {
	arrayDict := map[string]any{}
	arrayObjectIndex := len(objects)
	objects = append(objects, arrayDict)

	classDefinitionIndex := len(objects)
	objects = append(objects, buildClassDict("NSArray", "NSObject"))
	arrayDict["$class"] = plist.UID(classDefinitionIndex)
	itemRefs := make([]plist.UID, len(array))
	for index, item := range array {
		var uid plist.UID
		objects, uid = archive(item, objects)
		itemRefs[index] = uid
	}
	arrayDict["NS.objects"] = itemRefs
	return objects, plist.UID(arrayObjectIndex)
}

func serializeMutableArray(array, objects []any) ([]any, plist.UID) {
	arrayDict := map[string]any{}
	arrayObjectIndex := len(objects)
	objects = append(objects, arrayDict)

	classDefinitionIndex := len(objects)
	objects = append(objects, buildClassDict("NSMutableArray", "NSArray", "NSObject"))
	arrayDict["$class"] = plist.UID(classDefinitionIndex)
	itemRefs := make([]plist.UID, len(array))
	for index, item := range array {
		var uid plist.UID
		objects, uid = archive(item, objects)
		itemRefs[index] = uid
	}
	arrayDict["NS.objects"] = itemRefs
	return objects, plist.UID(arrayObjectIndex)
}

func serializeSet(set, objects []any) ([]any, plist.UID) {
	setDict := map[string]any{}
	setObjectIndex := len(objects)
	objects = append(objects, setDict)

	classDefinitionIndex := len(objects)
	objects = append(objects, buildClassDict("NSSet", "NSObject"))
	setDict["$class"] = plist.UID(classDefinitionIndex)
	itemRefs := make([]plist.UID, len(set))
	for index, item := range set {
		var uid plist.UID
		objects, uid = archive(item, objects)
		itemRefs[index] = uid
	}
	setDict["NS.objects"] = itemRefs
	return objects, plist.UID(setObjectIndex)
}

func serializeMap(mapObject map[string]any, objects []any, classDict map[string]any) ([]any, plist.UID) {
	dictDict := map[string]any{}
	dictionaryRef := len(objects)
	objects = append(objects, dictDict)

	index := len(objects)
	objects = append(objects, classDict)
	dictDict["$class"] = plist.UID(index)

	keyRefs := make([]plist.UID, len(mapObject))

	i := 0
	keys := make([]string, len(mapObject))
	for k := range mapObject {
		keys[i] = k
		i++
	}

	index = 0
	for _, key := range keys {
		var uid plist.UID
		objects, uid = archive(key, objects)
		keyRefs[index] = uid
		index++
	}
	dictDict["NS.keys"] = keyRefs

	index = 0
	valueRefs := make([]plist.UID, len(mapObject))
	for _, key := range keys {
		var uid plist.UID
		objects, uid = archive(mapObject[key], objects)
		valueRefs[index] = uid
		index++
	}
	dictDict["NS.objects"] = valueRefs

	return objects, plist.UID(dictionaryRef)
}

func isArray(object any) bool {
	return reflect.TypeOf(object).Kind() == reflect.Array
}

func isMap(object any) bool {
	return reflect.TypeOf(object).Kind() == reflect.Map
}
