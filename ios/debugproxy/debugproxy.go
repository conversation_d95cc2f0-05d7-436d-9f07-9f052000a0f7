package debugproxy

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net"
	"os"
	"path/filepath"
	"sync"
	"time"

	log "github.com/sirupsen/logrus"

	"github.com/danielpaulus/go-ios/ios"
)

const connectionJSONFileName = "connections.json"

// DebugProxy can be used to dump and modify communication between mac and host
type DebugProxy struct {
	mux               sync.Mutex
	serviceList       []PhoneServiceInformation
	connectionCounter int
	WorkingDir        string
}

// PhoneServiceInformation contains info about a service started on the phone via lockdown.
type PhoneServiceInformation struct {
	ServicePort uint16
	ServiceName string
	UseSSL      bool
}

// ProxyConnection keeps track of the pairRecord and uses an ID to identify connections.
type ProxyConnection struct {
	id         string
	pairRecord ios.PairRecord
	debugProxy *DebugProxy
	info       ConnectionInfo
	log        *log.Entry
	mux        sync.Mutex
	closed     bool
}

type ConnectionInfo struct {
	ConnectionPath string
	CreatedAt      time.Time
	ID             string
}

func (p *ProxyConnection) LogClosed() {
	p.mux.Lock()
	defer p.mux.Unlock()
	if p.closed {
		return
	}
	p.closed = true
	p.log.Debug("Connection closed")
}

func (d *DebugProxy) storeServiceInformation(serviceInfo PhoneServiceInformation) {
	d.mux.Lock()
	defer d.mux.Unlock()
	d.serviceList = append(d.serviceList, serviceInfo)
}

func (d *DebugProxy) retrieveServiceInfoByPort(port uint16) (PhoneServiceInformation, error) {
	d.mux.Lock()
	defer d.mux.Unlock()
	for _, element := range d.serviceList {
		if element.ServicePort == port {
			return element, nil
		}
	}
	return PhoneServiceInformation{}, fmt.Errorf("No Service found for port %d", port)
}

// NewDebugProxy creates a new Default proxy
func NewDebugProxy() *DebugProxy {
	return &DebugProxy{mux: sync.Mutex{}, serviceList: []PhoneServiceInformation{}}
}

// Launch moves the original /var/run/usbmuxd to /var/run/usbmuxd.real and starts the server at /var/run/usbmuxd
func (d *DebugProxy) Launch(device ios.DeviceEntry, binaryMode bool) error {
	list, _ := ios.ListDevices()
	if len(list.DeviceList) > 1 {
		return fmt.Errorf("dproxy currently does not work when more than one device is connected to the host. please disconnect all but one device")
	}
	if binaryMode {
		slog.Info("Launching proxy in full binary mode")
	}
	var pairRecord ios.PairRecord
	if !binaryMode {
		var err error
		pairRecord, err = ios.ReadPairRecord(device.Properties.SerialNumber)
		if err != nil {
			return err
		}
		slog.Info("Successfully retrieved pair record",
			"hostID", pairRecord.HostID,
			"device", device.Properties.SerialNumber)
	}
	socketPath, err := ios.ToUnixSocketPath(ios.GetUsbmuxdSocket())
	if err != nil {
		slog.Error("Invalid socket path", "error", err, "socket", ios.GetUsbmuxdSocket())
		return err
	}
	originalSocket, err := MoveSock(socketPath)
	if err != nil {
		slog.Error("Unable to move, lacking permissions?", "error", err, "socket", ios.GetUsbmuxdSocket())
		return err
	}
	d.setupDirectory()
	listener, err := net.Listen("unix", socketPath)
	if err != nil {
		slog.Error("Could not listen on usbmuxd socket, do I have access permissions?", "error", err)
		return err
	}
	if err := os.Chmod(socketPath, 0o777); err != nil {
		slog.Error("Could not change permission on usbmuxd socket", "error", err)
		return err
	}

	for {
		conn, err := listener.Accept()
		if err != nil {
			slog.Error("error with connection", "error", err)
		}
		slog.Info("connected")
		d.connectionCounter++
		id := fmt.Sprintf("#%d", d.connectionCounter)
		connectionPath := filepath.Join(
			".", d.WorkingDir, "connection-"+id+"-"+time.Now().UTC().Format("2006.01.02-************"),
		)

		err = os.MkdirAll(connectionPath, os.ModePerm)
		if err != nil {
			slog.Error("failed mkdirall in connected", "error", err)
		}

		info := ConnectionInfo{ConnectionPath: connectionPath, CreatedAt: time.Now(), ID: id}
		d.addConnectionInfoToJsonFile(info)

		bindumpHostProxyFile := filepath.Join(connectionPath, "bindump-hostservice-to-proxy.txt")

		if !binaryMode {
			// if the proxy is in full binary mode, there is no point in creating another binary dump
			slog.Info("Creating binary dump of all communication between MAC OS and debugproxy",
				"file", bindumpHostProxyFile)
			conn = NewDumpingConn(bindumpHostProxyFile, conn)
		}

		startProxyConnection(conn, originalSocket, pairRecord, d, info, binaryMode)
	}
}

func startProxyConnection(
	conn net.Conn, originalSocket string, pairRecord ios.PairRecord, debugProxy *DebugProxy, info ConnectionInfo,
	binaryMode bool,
) {
	slog.Info("starting tunnel")
	devConn, err := ios.NewDeviceConnection(originalSocket)
	if err != nil {
		slog.Error("failed to create device connection", "error", err)
		return
	}

	logger := log.WithFields(log.Fields{"id": info.ID})
	p := ProxyConnection{info.ID, pairRecord, debugProxy, info, logger, sync.Mutex{}, false}

	if binaryMode {
		binOnUnixSocket := BinaryForwardingProxy{
			ios.NewDeviceConnectionWithConn(conn), NewBinDumpOnly(
				"does not matter", filepath.Join(info.ConnectionPath, "rawbindump-from-host-service.bin"), logger,
			),
		}
		binToDevice := BinaryForwardingProxy{
			devConn,
			NewBinDumpOnly("does not matter", filepath.Join(info.ConnectionPath, "rawbindump-from-device.bin"), logger),
		}
		go proxyBinDumpConnection(&p, binOnUnixSocket, binToDevice)
		return
	}
	connListeningOnUnixSocket := ios.NewUsbMuxConnection(ios.NewDeviceConnectionWithConn(conn))
	connectionToDevice := ios.NewUsbMuxConnection(devConn)
	go proxyUsbMuxConnection(&p, connListeningOnUnixSocket, connectionToDevice)
}

// Close moves /var/run/usbmuxd.real back to /var/run/usbmuxd and disconnects all active proxy connections
func (d *DebugProxy) Close() {
	slog.Info("Moving back original socket")
	socketPath, err := ios.ToUnixSocketPath(ios.GetUsbmuxdSocket())
	if err != nil {
		slog.Error("Invalid socket path", "error", err)
		return
	}
	err = MoveBack(socketPath)
	if err != nil {
		slog.Error("Failed moving back socket", "error", err)
	}
}

func (d *DebugProxy) setupDirectory() {
	newpath := filepath.Join(".", "dump-"+time.Now().UTC().Format("2006.01.02-************"))
	d.WorkingDir = newpath
	if err := os.MkdirAll(newpath, os.ModePerm); err != nil {
		slog.Error("Failed to create directory", "path", newpath, "error", err)
	}
}

func (d *DebugProxy) addConnectionInfoToJsonFile(connInfo ConnectionInfo) {
	file, err := os.OpenFile(
		filepath.Join(d.WorkingDir, connectionJSONFileName),
		os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0o644,
	)
	if err != nil {
		slog.Error("Failed to open file", "error", err)
	}
	data, err := json.Marshal(connInfo)
	if err != nil {
		slog.Error("Failed json", "error", err)
	}
	if _, err := file.Write(data); err != nil {
		slog.Error("Failed to write data", "error", err)
	}
	if _, err := io.WriteString(file, "\n"); err != nil {
		slog.Error("Failed to write newline", "error", err)
	}
	if err := file.Close(); err != nil {
		slog.Error("Failed to close file", "error", err)
	}
}

func (p *ProxyConnection) logJSONMessageFromDevice(msg map[string]any) {
	const outPath = "jsondump.json"
	msg["direction"] = "device->host"
	writeJSON(filepath.Join(p.info.ConnectionPath, outPath), msg)
}

func (p *ProxyConnection) logJSONMessageToDevice(msg map[string]any) {
	const outPath = "jsondump.json"
	msg["direction"] = "host->device"
	writeJSON(filepath.Join(p.info.ConnectionPath, outPath), msg)
}

func writeJSON(filePath string, JSON any) {
	file, err := os.OpenFile(
		filePath,
		os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0o644,
	)
	if err != nil {
		panic(fmt.Sprintf("Could not write to file err: %v filepath:'%s'", err, filePath))
	}
	jsonmsg, err := json.Marshal(JSON)
	if err != nil {
		slog.Warn("Error encoding to json", "data", JSON, "error", err)
	}
	if _, err := file.Write(jsonmsg); err != nil {
		slog.Error("Failed to write JSON", "error", err)
	}
	if _, err := io.WriteString(file, "\n"); err != nil {
		slog.Error("Failed to write newline", "error", err)
	}
	if err := file.Close(); err != nil {
		slog.Error("Failed to close file", "error", err)
	}
}
