package debugproxy

import (
	"bytes"
	"fmt"
	"io"

	log "github.com/sirupsen/logrus"
	"howett.net/plist"

	"github.com/danielpaulus/go-ios/ios"
)

func proxyUsbMuxConnection(p *ProxyConnection, muxOnUnixSocket, muxToDevice *ios.UsbMuxConnection) {
	defer func() {
		log.Println("done") // Println executes normally even if there is a panic
		if x := recover(); x != nil {
			log.Printf("run time panic, moving back socket %v", x)
			err := MoveBack(ios.ToUnixSocketPath(ios.GetUsbmuxdSocket()))
			if err != nil {
				log.WithFields(log.Fields{"error": err}).Error("Failed moving back socket")
			}
			panic(x)
		}
	}()
	for {
		request, err := muxOnUnixSocket.ReadMuxMessage()
		if err != nil {
			muxOnUnixSocket.ReleaseDeviceConnection().Close()
			muxToDevice.ReleaseDeviceConnection().Close()
			if err == io.EOF {
				p.LogClosed()
				return
			}
			p.log.Info("Failed reading UsbMuxMessage", err)
			return
		}

		var decodedRequest map[string]any
		decoder := plist.NewDecoder(bytes.NewReader(request.Payload))
		err = decoder.Decode(&decodedRequest)
		if err != nil {
			p.log.Info("Failed decoding MuxMessage", request, err)
		}
		p.logJSONMessageToDevice(map[string]any{"header": request.Header, "payload": decodedRequest, "type": "USBMUX"})

		p.log.WithFields(log.Fields{"ID": p.id, "direction": "host->device"}).Trace(decodedRequest)
		if decodedRequest["MessageType"] == "Connect" {
			handleConnect(request, decodedRequest, p, muxOnUnixSocket, muxToDevice)
			return
		}

		err = muxToDevice.SendMuxMessage(request)

		if decodedRequest["MessageType"] == "ReadPairRecord" {
			handleReadPairRecord(p, muxOnUnixSocket, muxToDevice)
			continue
		}
		if err != nil {
			panic(fmt.Sprintf("Failed forwarding message to device: %+v", request))
		}
		if decodedRequest["MessageType"] == "Listen" {
			handleListen(p, muxOnUnixSocket, muxToDevice)
			return
		}

		response, err := muxToDevice.ReadMuxMessage()
		if err != nil {
			p.log.Error("Failed muxToDevice.ReadMessage()", request, err)
		}
		var decodedResponse map[string]any
		decoder = plist.NewDecoder(bytes.NewReader(response.Payload))
		err = decoder.Decode(&decodedResponse)
		if err != nil {
			p.log.Error("Failed decoding MuxMessage", decodedResponse, err)
		}
		p.logJSONMessageFromDevice(
			map[string]any{
				"header": response.Header, "payload": decodedResponse, "type": "USBMUX",
			},
		)
		p.log.WithFields(log.Fields{"ID": p.id, "direction": "device->host"}).Trace(decodedResponse)
		err = muxOnUnixSocket.SendMuxMessage(response)
		if err != nil {
			p.log.Error("Failed muxOnUnixSocket.SendMuxMessage(response)", request, err)
		}
	}
}

func handleReadPairRecord(p *ProxyConnection, muxOnUnixSocket, muxToDevice *ios.UsbMuxConnection) {
	response, err := muxToDevice.ReadMuxMessage()
	if err != nil {
		p.log.Errorf("Failed to read message from device: %v", err)
		return
	}

	var decodedResponse map[string]any
	decoder := plist.NewDecoder(bytes.NewReader(response.Payload))
	err = decoder.Decode(&decodedResponse)
	if err != nil {
		p.log.Warn("failed decoding MuxMessage", decodedResponse, err)
	}

	pairRecord, err := ios.PairRecordFromBytes(decodedResponse["PairRecordData"].([]byte))
	if err != nil {
		p.log.Errorf("failed decoding pair record: %v", err)
		return
	}

	pairRecord.DeviceCertificate = pairRecord.HostCertificate
	decodedResponse["PairRecordData"] = []byte(ios.ToPlist(pairRecord))
	newPayload := []byte(ios.ToPlist(decodedResponse))
	response.Payload = newPayload
	response.Header.Length = uint32(len(newPayload) + 16)
	p.logJSONMessageFromDevice(map[string]any{"header": response.Header, "payload": decodedResponse, "type": "USBMUX"})
	p.log.WithFields(log.Fields{"ID": p.id, "direction": "device->host"}).Trace(decodedResponse)
	if err := muxOnUnixSocket.SendMuxMessage(response); err != nil {
		p.log.Errorf("Failed to send mux message to unix socket: %v", err)
	}
}

func handleConnect(
	connectRequest ios.UsbMuxMessage, decodedConnectRequest map[string]any, p *ProxyConnection,
	muxOnUnixSocket, muxToDevice *ios.UsbMuxConnection,
) {
	var port uint16
	portFromPlist := decodedConnectRequest["PortNumber"]
	switch typedPort := portFromPlist.(type) {
	case uint64:
		port = uint16(typedPort)
	case int64:
		port = uint16(typedPort)
	}

	if port == ios.Lockdownport {
		p.log.Trace("Connect to Lockdown")
		handleConnectToLockdown(connectRequest, p, muxOnUnixSocket, muxToDevice)
	} else {
		info, err := p.debugProxy.retrieveServiceInfoByPort(ios.Ntohs(port))
		if err != nil {
			panic(
				fmt.Sprintf(
					"ServiceInfo for port: %d not found, this is a bug :-)reqheader: %+v repayload: %x", port,
					connectRequest.Header, connectRequest.Payload,
				),
			)
		}
		p.log.Infof("Connection to service '%s' detected on port %d", info.ServiceName, info.ServicePort)
		handleConnectToService(connectRequest, p, muxOnUnixSocket, muxToDevice, info)
	}
}

func handleConnectToLockdown(
	connectRequest ios.UsbMuxMessage, p *ProxyConnection,
	muxOnUnixSocket, muxToDevice *ios.UsbMuxConnection,
) {
	err := muxToDevice.SendMuxMessage(connectRequest)
	if err != nil {
		panic("Failed sending muxmessage to device")
	}
	connectResponse, err := muxToDevice.ReadMuxMessage()
	if err != nil {
		p.log.Errorf("Failed to read connect response from device: %v", err)
		return
	}
	if err := muxOnUnixSocket.SendMuxMessage(connectResponse); err != nil {
		p.log.Errorf("Failed to send connect response to unix socket: %v", err)
		return
	}

	lockdownToDevice := ios.NewLockDownConnection(muxToDevice.ReleaseDeviceConnection())
	lockdownOnUnixSocket := ios.NewLockDownConnection(muxOnUnixSocket.ReleaseDeviceConnection())
	proxyLockDownConnection(p, lockdownOnUnixSocket, lockdownToDevice)
}

func handleListen(p *ProxyConnection, muxOnUnixSocket, muxToDevice *ios.UsbMuxConnection) {
	go func() {
		// use this to detect when the conn is closed. There shouldn't be any messages received ever.
		_, err := muxOnUnixSocket.ReadMuxMessage()
		if err == io.EOF {
			muxOnUnixSocket.ReleaseDeviceConnection().Close()
			muxToDevice.ReleaseDeviceConnection().Close()
			p.LogClosed()
			return
		}
		p.log.WithFields(log.Fields{"error": err}).Error("Unexpected error on read for LISTEN connection")
	}()

	for {
		response, err := muxToDevice.ReadMuxMessage()
		if err != nil {
			// TODO: ugly, improve
			d := muxOnUnixSocket.ReleaseDeviceConnection()
			d1 := muxToDevice.ReleaseDeviceConnection()
			if d != nil {
				d.Close()
			}
			if d1 != nil {
				d1.Close()
			}

			p.LogClosed()
			return
		}
		var decodedResponse map[string]any
		decoder := plist.NewDecoder(bytes.NewReader(response.Payload))
		err = decoder.Decode(&decodedResponse)
		if err != nil {
			p.log.Info("Failed decoding MuxMessage", decodedResponse, err)
		}
		p.logJSONMessageFromDevice(
			map[string]any{
				"header": response.Header, "payload": decodedResponse, "type": "USBMUX",
			},
		)
		p.log.WithFields(log.Fields{"ID": p.id, "direction": "device->host"}).Trace(decodedResponse)
		err = muxOnUnixSocket.SendMuxMessage(response)
		if err != nil {
			p.log.Info("Failed muxOnUnixSocket.SendMuxMessage(response)", decodedResponse, err)
		}
	}
}
