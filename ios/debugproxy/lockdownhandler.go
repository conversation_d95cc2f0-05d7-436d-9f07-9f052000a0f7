package debugproxy

import (
	"bytes"
	"io"

	log "github.com/sirupsen/logrus"
	"howett.net/plist"

	"github.com/danielpaulus/go-ios/ios"
)

func proxyLockDownConnection(p *ProxyConnection, lockdownOnUnixSocket, lockdownToDevice *ios.LockDownConnection) {
	for {
		request, err := lockdownOnUnixSocket.ReadMessage()
		if err != nil {
			lockdownOnUnixSocket.Close()
			lockdownToDevice.Close()
			if err == io.EOF {
				p.LogClosed()
				return
			}
			p.log.Info("Failed reading LockdownMessage", err)
			return
		}

		var decodedRequest map[string]any
		decoder := plist.NewDecoder(bytes.NewReader(request))
		err = decoder.Decode(&decodedRequest)
		if err != nil {
			p.log.Info("Failed decoding LockdownMessage", request, err)
		}
		p.logJSONMessageToDevice(map[string]any{"payload": decodedRequest, "type": "LOCKDOWN"})
		p.log.WithFields(log.Fields{"ID": p.id, "direction": "host2device"}).Info(decodedRequest)

		err = lockdownToDevice.Send(decodedRequest)
		if err != nil {
			p.log.Errorf("Failed forwarding message to device: %x", request)
		}
		p.log.Info("done sending to device")
		response, err := lockdownToDevice.ReadMessage()
		if err != nil {
			log.Errorf("error reading from device: %+v", err)
			response, err = lockdownToDevice.ReadMessage()
			log.Infof("second read: %+v %+v", response, err)
		}

		var decodedResponse map[string]any
		decoder = plist.NewDecoder(bytes.NewReader(response))
		err = decoder.Decode(&decodedResponse)
		if err != nil {
			p.log.Info("Failed decoding LockdownMessage", decodedResponse, err)
		}
		p.logJSONMessageFromDevice(map[string]any{"payload": decodedResponse, "type": "LOCKDOWN"})
		p.log.WithFields(log.Fields{"ID": p.id, "direction": "device2host"}).Info(decodedResponse)

		err = lockdownOnUnixSocket.Send(decodedResponse)
		if err != nil {
			p.log.Info("Failed sending LockdownMessage from device to host service", decodedResponse, err)
		}
		if decodedResponse["EnableSessionSSL"] == true {
			if err := lockdownToDevice.EnableSessionSsl(p.pairRecord); err != nil {
				p.log.Errorf("Failed to enable session SSL for device connection: %v", err)
			}
			if err := lockdownOnUnixSocket.EnableSessionSslServerMode(p.pairRecord); err != nil {
				p.log.Errorf("Failed to enable session SSL server mode for unix socket: %v", err)
			}
		}
		if decodedResponse["Request"] == "StartService" && decodedResponse["Error"] == nil {
			useSSL := false
			if decodedResponse["EnableServiceSSL"] != nil {
				useSSL = decodedResponse["EnableServiceSSL"].(bool)
			}
			info := PhoneServiceInformation{
				ServicePort: uint16(decodedResponse["Port"].(uint64)),
				ServiceName: decodedResponse["Service"].(string),
				UseSSL:      useSSL,
			}

			p.log.Debugf("Detected Service Start:%+v", info)
			p.debugProxy.storeServiceInformation(info)
		}

		if decodedResponse["Request"] == "StopSession" {
			p.log.Info("Stop Session detected, disabling SSL")
			lockdownOnUnixSocket.DisableSessionSSL()
			lockdownToDevice.DisableSessionSSL()
		}
	}
}
