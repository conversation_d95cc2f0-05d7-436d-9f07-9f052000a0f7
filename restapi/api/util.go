package api

import (
	"encoding/json"
	"fmt"
	"log/slog"
	"math"
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"
)

type GenericResponse struct {
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
}

// GetVersion reads the contents of the file version.txt and returns it.
// If the file cannot be read, it returns "could not read version"
func GetVersion() string {
	version, err := os.ReadFile("version.txt")
	if err != nil {
		return "could not read version"
	}
	return string(version)
}

func MustMarshal(v any) string {
	b, err := json.Marshal(v)
	if err != nil {
		panic(err)
	}
	return string(b)
}

var timeFormat = "02/Jan/2006:15:04:05 -0700"

// MyLogger creates a gin middleware for logging using slog
func MyLogger(notLogged ...string) gin.HandlerFunc {
	hostname, err := os.Hostname()
	if err != nil {
		hostname = "unknown"
	}

	var skip map[string]struct{}

	if length := len(notLogged); length > 0 {
		skip = make(map[string]struct{}, length)

		for _, p := range notLogged {
			skip[p] = struct{}{}
		}
	}

	return func(c *gin.Context) {
		// other handler can change c.Path so:
		path := c.Request.URL.Path
		start := time.Now()
		c.Next()
		stop := time.Since(start)
		latency := int(math.Ceil(float64(stop.Nanoseconds()) / 1000000.0))
		statusCode := c.Writer.Status()
		clientIP := c.ClientIP()
		clientUserAgent := c.Request.UserAgent()
		referer := c.Request.Referer()
		dataLength := c.Writer.Size()
		if dataLength < 0 {
			dataLength = 0
		}

		if _, ok := skip[path]; ok {
			return
		}

		if len(c.Errors) > 0 {
			slog.Error("Request error",
				"hostname", hostname,
				"statusCode", statusCode,
				"latency", latency,
				"clientIP", clientIP,
				"method", c.Request.Method,
				"path", path,
				"referer", referer,
				"dataLength", dataLength,
				"userAgent", clientUserAgent,
				"errors", c.Errors.ByType(gin.ErrorTypePrivate).String())
		} else {
			msg := fmt.Sprintf(
				"%s - %s [%s] \"%s %s\" %d %d \"%s\" \"%s\" (%dms)", clientIP, hostname, time.Now().Format(timeFormat),
				c.Request.Method, path, statusCode, dataLength, referer, clientUserAgent, latency,
			)
			if statusCode >= http.StatusInternalServerError {
				slog.Error(msg,
					"hostname", hostname,
					"statusCode", statusCode,
					"latency", latency,
					"clientIP", clientIP,
					"method", c.Request.Method,
					"path", path,
					"referer", referer,
					"dataLength", dataLength,
					"userAgent", clientUserAgent)
			} else if statusCode >= http.StatusBadRequest {
				slog.Warn(msg,
					"hostname", hostname,
					"statusCode", statusCode,
					"latency", latency,
					"clientIP", clientIP,
					"method", c.Request.Method,
					"path", path,
					"referer", referer,
					"dataLength", dataLength,
					"userAgent", clientUserAgent)
			} else {
				slog.Info(msg,
					"hostname", hostname,
					"statusCode", statusCode,
					"latency", latency,
					"clientIP", clientIP,
					"method", c.Request.Method,
					"path", path,
					"referer", referer,
					"dataLength", dataLength,
					"userAgent", clientUserAgent)
			}
		}
	}
}
