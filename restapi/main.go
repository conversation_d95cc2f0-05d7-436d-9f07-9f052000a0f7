package main

import (
	"log/slog"
	"os"

	"github.com/danielpaulus/go-ios/restapi/api"
)

// @title           Go-iOS API
// @version         0.01
// @description     Exposes go-ios features as REST API calls.
// @termsOfService  https://github.com/danielpaulus/go-ios

// @contact.name   <PERSON>
// @contact.url    https://github.com/danielpaulus/go-ios

// @license.name  MIT
// @license.url   https://opensource.org/licenses/MIT

// @host      localhost:8080
// @BasePath  /api/v1

// @securityDefinitions.basic  BasicAuth
func main() {
	slog.Info("starting go-iOS-API", "args", os.Args, "version", api.GetVersion())
	api.Main()
}
