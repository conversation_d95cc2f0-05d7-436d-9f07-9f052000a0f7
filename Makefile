# Makefile to build and run go-ios and the cdc-ncm network driver
# cdc-ncm needs to be executed with sudo on Linux for USB Access and setting
# up virtual TAP network devices.
# use Make build to build both binaries.
# Make run is a simple target that just runs the cdc-ncm driver with sudo
# For development, use "make up" to rebuild and run cdc-ncm quickly

SERVICE_NAME = go-ios
MAKEFILE_DIR := $(patsubst %/,%,$(dir $(abspath $(lastword $(MAKEFILE_LIST)))))
BIN_DIR := ${MAKEFILE_DIR}/bin

export GITLAB_GROUP = "github.com/danielpaulus/go-ios"

GCFLAGS := "-N -l"
LDFLAGS := "-s -w"

# check if `gofumpt` command exists
GOFUMPT_EXISTS := $(shell command -v gofumpt >/dev/null 2>&1 && echo 1 || echo 0)

GO_FORMAT_CMD := gofmt -s -w
ifeq (1, $(GOFUMPT_EXISTS))
GO_FORMAT_CMD = gofumpt -l -w -extra
endif

define cmd-build
	@if [ "$(2)" = "go-ncm" ]; then \
		SOURCE="$(MAKEFILE_DIR)/cdcncm/main.go"; \
	else \
		SOURCE="$(MAKEFILE_DIR)/main.go"; \
	fi; \
	if [ "$(1)" = "darwin" ]; then \
		PRE_ENV="GOOS=$(1)"; \
		TARGET="$(BIN_DIR)/$(2).$(1)"; \
	elif [ "$(1)" = "linux" ]; then \
		PRE_ENV="GOOS=$(1) GOARCH=amd64"; \
		TARGET="$(BIN_DIR)/$(2).$(1)"; \
	elif [ "$(1)" = "windows" ]; then \
		PRE_ENV="GOOS=$(1) GOARCH=amd64"; \
		TARGET="$(BIN_DIR)/$(2).$(1).exe"; \
	fi; \
	echo "build $$TARGET by $$PRE_ENV"; \
	eval "$$PRE_ENV go build -o $$TARGET -ldflags=\"$(LDFLAGS)\" -gcflags=\"$(GCFLAGS)\" $$SOURCE";
endef

.PHONY: fmt
fmt:
	@go list -f {{.Dir}} $(MAKEFILE_DIR)/... | xargs $(GO_FORMAT_CMD)
	@goimports -l -w -local $(GITLAB_GROUP) $(MAKEFILE_DIR)

.PHONY: lint
lint:
	@golangci-lint run -c $(MAKEFILE_DIR)/.golangci.yaml

.PHONY: mac
mac:
	$(call cmd-build,darwin,go-ios)

.PHONY: linux
linux:
	$(call cmd-build,linux,go-ios)

.PHONY: win
win:
	$(call cmd-build,windows,go-ios)

.PHONY: mac-ncm
mac-ncm:
	$(call cmd-build,darwin,go-ncm)

.PHONY: linux-ncm
linux-ncm:
	$(call cmd-build,linux,go-ncm)

.PHONY: win-ncm
win-ncm:
	$(call cmd-build,windows,go-ncm)
