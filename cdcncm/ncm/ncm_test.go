package ncm

//TODO: fix later
/*
func TestReadCompleteDatagram(t *testing.T) {
	tests := []struct {
		name         string
		inputBase64  string
		outputBase64 string
	}{
		{
			name:         "1",
			inputBase64:  "TkNNSAwAAwBxAQwATkNNMDwAAABKACcBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAzMwAAAPvCYOcZq2GG3WABAgAA8RH//oAAAAAAAADAYOf//hmrYf8CAAAAAAAAAAAAAAAAAPsU6RTpAPG5pwAAhAAAAAADAAAAAglfc2VydmljZXMHX2Rucy1zZARfdWRwBWxvY2FsAAAMAAEAABGUABYOX3JlbW90ZXBhaXJpbmcEX3RjcMAjBmlQaG9uZcAjAByAAQAAAHgAEP6AAAAAAAAAwGDn//4Zq2EBMQE2AUIBQQE5ATEBRQFGAUYBRgE3AUUBMAE2ATABQwEwATABMAEwATABMAEwATABMAEwATABMAEwATgBRQFGA2lwNgRhcnBhAAAMgAEAAAB4AALASsBKAC+AAQAAAHgACMBKAAQAAAAIwG0AL4ABAAAAeAAGwG0AAgAI",
			outputBase64: "MzMAAAD7wmDnGathht1gAQIAAPER//6AAAAAAAAAwGDn//4Zq2H/AgAAAAAAAAAAAAAAAAD7FOkU6QDxuacAAIQAAAAAAwAAAAIJX3NlcnZpY2VzB19kbnMtc2QEX3VkcAVsb2NhbAAADAABAAARlAAWDl9yZW1vdGVwYWlyaW5nBF90Y3DAIwZpUGhvbmXAIwAcgAEAAAB4ABD+gAAAAAAAAMBg5//+GathATEBNgFCAUEBOQExAUUBRgFGAUYBNwFFATABNgEwAUMBMAEwATABMAEwATABMAEwATABMAEwATABMAE4AUUBRgNpcDYEYXJwYQAADIABAAAAeAACwErASgAvgAEAAAB4AAjASgAEAAAACMBtAC+AAQAAAHgABsBtAAIACA==",
		},
		{
			name:         "2",
			inputBase64:  "TkNNSAwAAQD0AAwATkNNMIwAAACaAFoAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAzMwAAAPvCYOcZq2GG3WABAgABShH//oAAAAAAAADAYOf//hmrYf8CAAAAAAAAAAAAAAAAAPsU6RTpAUri2wAAhAAAAAAEAAAAAyQ3MDU0NDMzAAAAFsJg5xmrYYbdYAAAAAAkAAH+gAAAAAAAAMBg5//+Gath/wIAAAAAAAAAAAAAAAAAFjoAAQAFAgAAjwBzsgAAAAEEAAAA/wIAAAAAAAAAAAAB/xmrYQ==",
			outputBase64: "MzMAAAD7wmDnGathht1gAQIAAUoR//6AAAAAAAAAwGDn//4Zq2H/AgAAAAAAAAAAAAAAAAD7FOkU6QFK4tsAAIQAAAAABAAAAAMkNzA1NDQzMwAAABbCYOcZq2GG3WAAAAAAJAAB/oAAAAAAAADAYOf//hmrYf8CAAAAAAAAAAAAAAAAABY6AAEABQIAAI8Ac7IAAAABBAAAAP8CAAAAAAAAAAAAAf8Zq2E=",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p, _ := base64.StdEncoding.DecodeString(tt.inputBase64)
			expected, _ := base64.StdEncoding.DecodeString(tt.outputBase64)

			buf := bytes.NewBuffer(p)

			x := NewWrapper(buf, buf)

			r := make([]byte, 512)

			n, err := x.Read(r)
			assert.NoError(t, err)
			assert.Equal(t, 295, n)
			assert.Equal(t, expected, r[:n])
		})
	}
	//
	//p, _ := base64.StdEncoding.DecodeString("TkNNSAwAAwBxAQwATkNNMDwAAABKACcBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAzMwAAAPvCYOcZq2GG3WABAgAA8RH//oAAAAAAAADAYOf//hmrYf8CAAAAAAAAAAAAAAAAAPsU6RTpAPG5pwAAhAAAAAADAAAAAglfc2VydmljZXMHX2Rucy1zZARfdWRwBWxvY2FsAAAMAAEAABGUABYOX3JlbW90ZXBhaXJpbmcEX3RjcMAjBmlQaG9uZcAjAByAAQAAAHgAEP6AAAAAAAAAwGDn//4Zq2EBMQE2AUIBQQE5ATEBRQFGAUYBRgE3AUUBMAE2ATABQwEwATABMAEwATABMAEwATABMAEwATABMAEwATgBRQFGA2lwNgRhcnBhAAAMgAEAAAB4AALASsBKAC+AAQAAAHgACMBKAAQAAAAIwG0AL4ABAAAAeAAGwG0AAgAI")
	//expected, _ := base64.StdEncoding.DecodeString("MzMAAAD7wmDnGathht1gAQIAAPER//6AAAAAAAAAwGDn//4Zq2H/AgAAAAAAAAAAAAAAAAD7FOkU6QDxuacAAIQAAAAAAwAAAAIJX3NlcnZpY2VzB19kbnMtc2QEX3VkcAVsb2NhbAAADAABAAARlAAWDl9yZW1vdGVwYWlyaW5nBF90Y3DAIwZpUGhvbmXAIwAcgAEAAAB4ABD+gAAAAAAAAMBg5//+GathATEBNgFCAUEBOQExAUUBRgFGAUYBNwFFATABNgEwAUMBMAEwATABMAEwATABMAEwATABMAEwATABMAE4AUUBRgNpcDYEYXJwYQAADIABAAAAeAACwErASgAvgAEAAAB4AAjASgAEAAAACMBtAC+AAQAAAHgABsBtAAIACA==")
	//
	//buf := bytes.NewBuffer(p)
	//
	//x := NewWrapper(buf, buf)
	//
	//r := make([]byte, 512)
	//
	//n, err := x.Read(r)
	//assert.NoError(t, err)
	//assert.Equal(t, 295, n)
	//assert.Equal(t, expected, r[:n])
}

func TestReadDatagramInMultipleSteps(t *testing.T) {
	p, _ := base64.StdEncoding.DecodeString("TkNNSAwAAwBxAQwATkNNMDwAAABKACcBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAzMwAAAPvCYOcZq2GG3WABAgAA8RH//oAAAAAAAADAYOf//hmrYf8CAAAAAAAAAAAAAAAAAPsU6RTpAPG5pwAAhAAAAAADAAAAAglfc2VydmljZXMHX2Rucy1zZARfdWRwBWxvY2FsAAAMAAEAABGUABYOX3JlbW90ZXBhaXJpbmcEX3RjcMAjBmlQaG9uZcAjAByAAQAAAHgAEP6AAAAAAAAAwGDn//4Zq2EBMQE2AUIBQQE5ATEBRQFGAUYBRgE3AUUBMAE2ATABQwEwATABMAEwATABMAEwATABMAEwATABMAEwATgBRQFGA2lwNgRhcnBhAAAMgAEAAAB4AALASsBKAC+AAQAAAHgACMBKAAQAAAAIwG0AL4ABAAAAeAAGwG0AAgAI")

	buf := bytes.NewBuffer(p)

	x := NewWrapper(buf, buf)

	r := make([]byte, 6)
	n, err := x.Read(r)
	require.NoError(t, err)
	assert.Equal(t, 6, n)
	assert.Equal(t, []byte{0x33, 0x33, 0x0, 0x0, 0x0, 0xfb}, r)

	r = make([]byte, 6)
	n, err = x.Read(r)
	require.NoError(t, err)
	assert.Equal(t, 6, n)
	assert.Equal(t, []byte{0xc2, 0x60, 0xe7, 0x19, 0xab, 0x61}, r)
}

func TestReadWrite(t *testing.T) {
	buf := bytes.NewBuffer(nil)

	x := NewWrapper(buf, buf)

	data := []byte{0x1, 0x2, 0x3, 0x4}

	written, err := x.Write(data)
	assert.NoError(t, err)
	assert.Equal(t, 4, written)

	r := make([]byte, 10)
	read, err := x.Read(r)
	assert.NoError(t, err)
	assert.Equal(t, 4, read)
	assert.Equal(t, data, r[:read])
}
*/
